import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:packagingwala_web/widgets/smart_svg_icon.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/country_picker.dart';
import '../widgets/platform_icon.dart';
import '../models/order_model.dart';
import '../models/country_model.dart';

// Local controller for the edit order dialog
class _EditOrderDialogController extends GetxController {
  // Text controllers
  final TextEditingController editCustomerNameController = TextEditingController();
  final TextEditingController editMobileNumberController = TextEditingController();
  final TextEditingController editDeliveryDateController = TextEditingController();
  final TextEditingController editOrderStatusController = TextEditingController();
  final TextEditingController editOrderAmountController = TextEditingController();
  final TextEditingController editTrackingIdController = TextEditingController();

  // Observable variables
  final Rx<CountryModel> editSelectedCountry = CountryModel(
    name: 'India',
    flag: '🇮🇳',
    code: 'IN',
    dialCode: '+91',
  ).obs;

  @override
  void onClose() {
    editCustomerNameController.dispose();
    editMobileNumberController.dispose();
    editDeliveryDateController.dispose();
    editOrderStatusController.dispose();
    editOrderAmountController.dispose();
    editTrackingIdController.dispose();
    super.onClose();
  }

  void prefillForm(OrderModel order) {
    editCustomerNameController.text = order.customerName;
    editMobileNumberController.text = order.customerPhone.replaceAll('+91', '').trim();
    editDeliveryDateController.text = _formatDateForInput(order.deliveryDate);
    editOrderStatusController.text = order.status;
    editOrderAmountController.text = order.amount.toString();
    editTrackingIdController.text = 'TRK${order.orderId.replaceAll('ORD-', '')}';
  }

  String _formatDateForInput(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void onEditCountrySelected(CountryModel country) {
    editSelectedCountry.value = country;
  }
}

class EditOrderDialog extends StatelessWidget {
  final OrderModel order;
  final Function(OrderModel) onSave;

  const EditOrderDialog({
    super.key,
    required this.order,
    required this.onSave,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(_EditOrderDialogController());
    controller.prefillForm(order);
    MySize().init(context);

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size40,
      ),
      child: Container(
        // width: MySize.size700,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9,
          maxWidth: MediaQuery.of(context).size.width * 0.8,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MySize.size16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(MySize.size24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer Information Section
                    _buildCustomerInformationSection(controller),

                    SizedBox(height: MySize.size24),

                    // Order Details Section
                    _buildOrderDetailsSection(controller),

                    SizedBox(height: MySize.size24),

                    // Shipping Address Section
                    _buildShippingAddressSection(controller),
                  ],
                ),
              ),
            ),

            // Footer Buttons
            _buildFooterButtons(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(MySize.size8),
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: PlatformIcon(
              iconName: 'edit',
              size: MySize.size20,
              color: AppColors.primaryColor,
            ),
          ),
          SizedBox(width: MySize.size12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Edit Order',
                  style: TextStyle(
                    fontSize: MySize.size20,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  'Update order details for ${order.orderId}',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              Get.delete<_EditOrderDialogController>();
              Navigator.of(Get.context!).pop();
            },
            child: Container(
              padding: EdgeInsets.all(MySize.size8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(MySize.size8),
              ),
              child: PlatformIcon(
                iconName: 'close',
                size: MySize.size16,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerInformationSection(_EditOrderDialogController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Row(
          children: [
            PlatformIcon(
              iconName: 'person',
              size: MySize.size20,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: MySize.size8),
            Text(
              'Customer Information',
              style: TextStyle(
                fontSize: MySize.size16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),

        SizedBox(height: MySize.size16),

        // Customer Name and Mobile Number Row
        Row(
          children: [
            // Customer Name
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Customer Name',
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: MySize.size8),
                  CustomTextField(
                    controller: controller.editCustomerNameController,
                    hintText: 'Enter customer name',
                    fillColor: Colors.white,
                    borderColor: AppColors.borderColor,
                    borderRadius: MySize.size8,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: MySize.size16,
                      vertical: MySize.size12,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(width: MySize.size16),

            // Mobile Number
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Mobile Number',
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: MySize.size8),
                  Row(
                    children: [
                      // Country Picker
                      Obx(
                        () => CountryPicker(
                          selectedCountry: controller.editSelectedCountry.value,
                          onCountrySelected: controller.onEditCountrySelected,
                          width: MySize.size62,
                        ),
                      ),
                      SizedBox(width: MySize.size8),
                      // Mobile Number Field
                      Expanded(
                        child: CustomTextField(
                          controller: controller.editMobileNumberController,
                          hintText: 'Enter mobile number',
                          keyboardType: TextInputType.phone,
                          fillColor: Colors.white,
                          borderColor: AppColors.borderColor,
                          borderRadius: MySize.size8,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: MySize.size16,
                            vertical: MySize.size12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrderDetailsSection(_EditOrderDialogController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Row(
          children: [
            PlatformIcon(
              iconName: 'order',
              size: MySize.size20,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: MySize.size8),
            Text(
              'Order Details',
              style: TextStyle(
                fontSize: MySize.size16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),

        SizedBox(height: MySize.size16),

        // First Row: Delivery Date and Order Status
        Row(
          children: [
            // Delivery Date
            Expanded(
              child: _buildFormField(
                label: 'Delivery Date',
                controller: controller.editDeliveryDateController,
                hintText: 'Select delivery date',
              ),
            ),

            SizedBox(width: MySize.size16),

            // Order Status
            Expanded(
              child: _buildFormField(
                label: 'Order Status',
                controller: controller.editOrderStatusController,
                hintText: 'Enter order status',
              ),
            ),
          ],
        ),

        SizedBox(height: MySize.size16),

        // Second Row: Order Amount and Tracking ID
        Row(
          children: [
            // Order Amount
            Expanded(
              child: _buildFormField(
                label: 'Order Amount',
                controller: controller.editOrderAmountController,
                hintText: 'Enter order amount',
                keyboardType: TextInputType.number,
              ),
            ),

            SizedBox(width: MySize.size16),

            // Tracking ID
            Expanded(
              child: _buildFormField(
                label: 'Tracking Id',
                controller: controller.editTrackingIdController,
                hintText: 'Enter tracking ID',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildShippingAddressSection(_EditOrderDialogController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Row(
          children: [
            PlatformIcon(
              iconName: 'location',
              size: MySize.size20,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: MySize.size8),
            Text(
              'Shipping Address',
              style: TextStyle(
                fontSize: MySize.size16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),

        SizedBox(height: MySize.size16),

        // Address Display Container
        Container(
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            color: AppColors.backgroundColor,
            borderRadius: BorderRadius.circular(MySize.size8),
            border: Border.all(color: AppColors.borderColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Current Address',
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: MySize.size8),
              Text(
                'Venkat Nagar 4th Street Yanam\nPincode: 533464',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOrderItemCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: AppColors.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              _getProductName(),
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Text(
            '₹ ${order.amount.toStringAsFixed(0)}',
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  // Get product name based on order data
  String _getProductName() {
    // Generate product name based on order characteristics
    final productTypes = [
      'Custom Packaging Box',
      'Premium Gift Box',
      'Eco-Friendly Package',
      'Luxury Product Box',
      'Standard Shipping Box',
      'Branded Packaging',
      'Corrugated Box',
      'Display Package',
    ];

    // Use order ID to consistently generate the same product name for the same order
    final index = order.orderId.hashCode.abs() % productTypes.length;
    return productTypes[index];
  }

  Widget _buildFooterButtons(_EditOrderDialogController controller) {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Items Heading
          Row(
            children: [
              SmartIcon(
                assetPath: 'assets/icons/order_icon.svg',
                height: MySize.size20,
                width: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Order Items',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size16),

          // Responsive Order Item Card and Buttons Layout
          LayoutBuilder(
            builder: (context, constraints) {
              final isMobile = constraints.maxWidth <= 600;

              if (isMobile) {
                // Mobile Layout: Card on top, buttons below
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Order Item Card
                    _buildOrderItemCard(),

                    SizedBox(height: MySize.size16),

                    // Buttons Row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        _buildCancelButton(),
                        SizedBox(width: MySize.size12),
                        _buildSaveButton(controller),
                      ],
                    ),
                  ],
                );
              } else {
                // Desktop Layout: Card on left, buttons on right
                return Row(
                  children: [
                    // Order Item Card on the left
                    Expanded(
                      child: _buildOrderItemCard(),
                    ),

                    SizedBox(width: MySize.size24),

                    // Buttons on the right
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildCancelButton(),
                        SizedBox(width: MySize.size12),
                        _buildSaveButton(controller),
                      ],
                    ),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCancelButton() {
    return OutlinedButton(
      onPressed: () => Get.back(),
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: AppColors.borderColor),
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size24,
          vertical: MySize.size12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
      ),
      child: Text(
        'Cancel',
        style: TextStyle(
          fontSize: MySize.size14,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }

  Widget _buildSaveButton(_EditOrderDialogController controller) {
    return ElevatedButton(
      onPressed: () => _handleSave(controller),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size24,
          vertical: MySize.size12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
      ),
      child: Text(
        'Save changes',
        style: TextStyle(
          fontSize: MySize.size14,
          fontWeight: FontWeight.w500,
          color: Colors.black,
        ),
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        CustomTextField(
          controller: controller,
          hintText: hintText,
          keyboardType: keyboardType,
          fillColor: Colors.white,
          borderColor: AppColors.borderColor,
          borderRadius: MySize.size8,
          contentPadding: EdgeInsets.symmetric(
            horizontal: MySize.size16,
            vertical: MySize.size12,
          ),
        ),
      ],
    );
  }

  void _handleSave(_EditOrderDialogController controller) {
    // Create updated order with new data
    final updatedOrder = OrderModel(
      orderId: order.orderId,
      customerName: controller.editCustomerNameController.text,
      customerPhone: '${controller.editSelectedCountry.value.dialCode}${controller.editMobileNumberController.text}',
      orderDate: order.orderDate, // Keep original order date
      deliveryDate: _parseDate(controller.editDeliveryDateController.text) ?? order.deliveryDate,
      itemCount: order.itemCount, // Keep original item count
      amount: double.tryParse(controller.editOrderAmountController.text) ?? order.amount,
      status: controller.editOrderStatusController.text,
    );

    // Clean up controller and close dialog
    Get.delete<_EditOrderDialogController>();
    Navigator.of(Get.context!).pop();

    // Call the save callback
    onSave(updatedOrder);
  }

  DateTime? _parseDate(String dateString) {
    try {
      // Parse date in DD/MM/YYYY format
      final parts = dateString.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      // Return null if parsing fails
    }
    return null;
  }
}