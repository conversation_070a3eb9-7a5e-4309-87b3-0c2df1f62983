import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:packagingwala_web/constants/app_colors.dart';

/// A smart icon widget that automatically detects PNG or SVG files
/// and handles color filtering for both formats
class SmartIcon extends StatelessWidget {
  final String assetPath;
  final double? width;
  final double? height;
  final Color? color;

  const SmartIcon({
    super.key,
    required this.assetPath,
    this.width,
    this.height,
    this.color,
  });

  /// Mapping from SVG paths to PNG paths for converted files
  static const Map<String, String> _svgToPngMapping = {
    'assets/icons/approval_icon.svg': 'assets/icons/design_plate_approval_icon.png',
    'assets/icons/curing_icon.svg': 'assets/icons/curing_icon.png',
    'assets/icons/cylinder_icon.svg': 'assets/icons/cylinder_icon.png',
    'assets/icons/dispatched_icon.svg': 'assets/icons/dispatched_icon.png',
    'assets/icons/lamination_icon.svg': 'assets/icons/lamination_icon.png',
    'assets/icons/pasting_icon.svg': 'assets/icons/pasting_icon.png',
    'assets/icons/polyster_approved_icon.svg': 'assets/icons/polyster_sample_approved_icon.png',
    'assets/icons/polyster_printing_icon.svg': 'assets/icons/polyster_printing_icon.png',
    'assets/icons/pouching_icon.svg': 'assets/icons/pouching_icon.png',
    'assets/icons/sampling_icon.svg': 'assets/icons/sampling_icon.png',
    'assets/icons/sorting_icon.svg': 'assets/icons/sorting_icon.png',
    'assets/icons/zipper_icon.svg': 'assets/icons/zipper_addition_icon.png',
    'assets/icons/readytodispatch_icon.svg': 'assets/icons/ready_to_dispatch_icon.png',
    'assets/icons/onboarding_icon.svg': 'assets/icons/onboarding_icon.png',
    'assets/icons/designing_icon.svg': 'assets/icons/designing_icon.png',
    'assets/icons/heating_icon.svg': 'assets/icons/heating_icon.png',
    'assets/icons/packing_icon.svg': 'assets/icons/packing_icon.png',
    'assets/icons/slitting_icon.svg': 'assets/icons/slitting_icon.png',
    // Logo files - use the main logo PNG for both
    'assets/icons/logo_icon.svg': 'assets/png/logo.png',
    'assets/icons/logo_text.svg': 'assets/png/logo.png',
  };

  @override
  Widget build(BuildContext context) {
    String finalPath = assetPath;
    if (_svgToPngMapping.containsKey(assetPath)) {
      finalPath = _svgToPngMapping[assetPath]!;
    }

    if (finalPath.endsWith('.png')) {
      return _buildPngIcon(finalPath);
    } else {
      return _buildSvgIcon(finalPath);
    }
  }

  Widget _buildPngIcon(String path) {
    Widget image = Image.asset(
      path,
      width: width,
      height: height,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        return Icon(
          Icons.error,
          size: width ?? height ?? 24.0,
          color: color ?? AppColors.blackColor,
        );
      },
    );

    if (color != null) {
      return ColorFiltered(
        colorFilter: ColorFilter.mode(color!, BlendMode.srcIn),
        child: image,
      );
    }

    return image;
  }

  Widget _buildSvgIcon(String path) {
    return SvgPicture.asset(
      path,
      width: width,
      height: height,
      colorFilter: color != null
          ? ColorFilter.mode(color!, BlendMode.srcIn)
          : null,
      placeholderBuilder: (context) => Icon(
        Icons.error,
        size: width ?? height ?? 24.0,
        color: color ?? AppColors.blackColor,
      ),
    );
  }

  /// Factory constructor for creating icons with specific colors
  factory SmartIcon.colored({
    required String assetPath,
    required Color color,
    double? size,
  }) {
    return SmartIcon(
      assetPath: assetPath,
      width: size,
      height: size,
      color: color,
    );
  }

  /// Factory constructor for black icons
  factory SmartIcon.black({
    required String assetPath,
    double? size,
  }) {
    return SmartIcon.colored(
      assetPath: assetPath,
      color: AppColors.blackColor,
      size: size,
    );
  }

  /// Factory constructor for grey icons
  factory SmartIcon.grey({
    required String assetPath,
    double? size,
  }) {
    return SmartIcon.colored(
      assetPath: assetPath,
      color: AppColors.greyColor,
      size: size,
    );
  }

  /// Factory constructor for primary color icons
  factory SmartIcon.primary({
    required String assetPath,
    double? size,
  }) {
    return SmartIcon.colored(
      assetPath: assetPath,
      color: AppColors.primaryColor,
      size: size,
    );
  }
}
