import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../models/order_model.dart';

class DeleteOrderDialog extends StatelessWidget {
  final OrderModel order;
  final VoidCallback onConfirm;

  const DeleteOrderDialog({
    super.key,
    required this.order,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size40,
      ),
      child: Container(
        width: MySize.size400,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MySize.size16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(),

            // Content
            Padding(
              padding: EdgeInsets.all(MySize.size24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Warning message
                  _buildWarningMessage(),

                  SizedBox(height: MySize.size20),

                  // Additional warning text
                  _buildAdditionalWarning(),

                  SizedBox(height: MySize.size24),

                  // Action buttons
                  _buildActionButtons(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(MySize.size16),
          topRight: Radius.circular(MySize.size16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(MySize.size8),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: PlatformIcon(
              iconName: 'delete',
              size: MySize.size20,
              color: Colors.red,
            ),
          ),
          SizedBox(width: MySize.size12),
          Text(
            'Delete Order',
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.w600,
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarningMessage() {
    return RichText(
      text: TextSpan(
        style: TextStyle(
          fontSize: MySize.size14,
          color: AppColors.textPrimary,
          height: 1.5,
        ),
        children: [
          const TextSpan(text: 'Are you sure you want to delete order '),
          TextSpan(
            text: order.orderId,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const TextSpan(text: ' for customer '),
          TextSpan(
            text: order.customerName,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const TextSpan(text: '?'),
        ],
      ),
    );
  }

  Widget _buildAdditionalWarning() {
    return Container(
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(MySize.size8),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PlatformIcon(
            iconName: 'warning',
            size: MySize.size16,
            color: Colors.orange,
          ),
          SizedBox(width: MySize.size8),
          Expanded(
            child: Text(
              'This action cannot be undone. The order will be permanently removed from the system.',
              style: TextStyle(
                fontSize: MySize.size13,
                color: AppColors.textPrimary,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        // Cancel button
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppColors.borderColor),
              padding: EdgeInsets.symmetric(vertical: MySize.size12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(MySize.size8),
              ),
            ),
            child: Text(
              'Cancel',
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ),

        SizedBox(width: MySize.size12),

        // Delete button
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: EdgeInsets.symmetric(vertical: MySize.size12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(MySize.size8),
              ),
              elevation: 0,
            ),
            child: Text(
              'Delete Order',
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
