import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../models/order_model.dart';

class UpdatePriorityDialog extends StatefulWidget {
  final OrderModel order;
  final Function(OrderModel) onSave;

  const UpdatePriorityDialog({
    super.key,
    required this.order,
    required this.onSave,
  });

  @override
  State<UpdatePriorityDialog> createState() => _UpdatePriorityDialogState();
}

class _UpdatePriorityDialogState extends State<UpdatePriorityDialog> {
  late TextEditingController orderIdController;
  late TextEditingController notesController;
  String selectedPriority = 'High Priority';

  final List<Map<String, dynamic>> priorityOptions = [
    {
      'label': 'High Priority',
      'color': const Color(0xFFFF6B6B),
      'icon': Icons.circle,
    },
    {
      'label': 'Medium Priority',
      'color': const Color(0xFFFFB347),
      'icon': Icons.circle,
    },
    {
      'label': 'Low Priority',
      'color': const Color(0xFF6B73FF),
      'icon': Icons.circle,
    },
    {
      'label': 'No Priority',
      'color': const Color(0xFF9B59B6),
      'icon': Icons.close,
    },
  ];

  @override
  void initState() {
    super.initState();
    orderIdController = TextEditingController(text: widget.order.orderId);
    notesController = TextEditingController(text: widget.order.notes ?? '');
    selectedPriority = widget.order.priority ?? 'High Priority';
  }

  @override
  void dispose() {
    orderIdController.dispose();
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size40,
      ),
      child: Container(
        width: MySize.size600,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MySize.size16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(),

            // Content
            Padding(
              padding: EdgeInsets.all(MySize.size24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order ID Field
                  _buildOrderIdField(),

                  SizedBox(height: MySize.size20),

                  // Current Status Dropdown
                  _buildCurrentStatusDropdown(),

                  SizedBox(height: MySize.size20),

                  // Internal Notes Field
                  _buildInternalNotesField(),

                  SizedBox(height: MySize.size24),

                  // Action Buttons
                  _buildActionButtons(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(MySize.size16),
          topRight: Radius.circular(MySize.size16),
        ),
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Update Priority Status',
            style: TextStyle(
              fontSize: MySize.size20,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          GestureDetector(
            onTap: () => Get.back(),
            child: Container(
              padding: EdgeInsets.all(MySize.size4),
              child: PlatformIcon(
                iconName: 'close',
                size: MySize.size24,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderIdField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Id',
          style: TextStyle(
            fontSize: MySize.size16,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        CustomTextField(
          controller: orderIdController,
          readOnly: true,
          fillColor: AppColors.backgroundColor,
          borderColor: AppColors.borderColor,
          borderRadius: MySize.size8,
          contentPadding: EdgeInsets.symmetric(
            horizontal: MySize.size16,
            vertical: MySize.size12,
          ),
        ),
      ],
    );
  }

  Widget _buildCurrentStatusDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Current Status',
          style: TextStyle(
            fontSize: MySize.size16,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: MySize.size16),
          decoration: BoxDecoration(
            color: AppColors.backgroundColor,
            border: Border.all(color: AppColors.borderColor),
            borderRadius: BorderRadius.circular(MySize.size8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedPriority,
              isExpanded: true,
              icon: PlatformIcon(
                iconName: 'chevron_down',
                size: MySize.size20,
                color: AppColors.textSecondary,
              ),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    selectedPriority = newValue;
                  });
                }
              },
              items: priorityOptions.map<DropdownMenuItem<String>>((option) {
                return DropdownMenuItem<String>(
                  value: option['label'],
                  child: Row(
                    children: [
                      Icon(
                        option['icon'],
                        color: option['color'],
                        size: MySize.size16,
                      ),
                      SizedBox(width: MySize.size12),
                      Text(
                        option['label'],
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: option['color'],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInternalNotesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Internal Notes',
          style: TextStyle(
            fontSize: MySize.size16,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        CustomTextField(
          controller: notesController,
          hintText: 'Add Any Internal Notes about this Priority Change',
          maxLines: 5,
          fillColor: AppColors.backgroundColor,
          borderColor: AppColors.borderColor,
          borderRadius: MySize.size8,
          contentPadding: EdgeInsets.symmetric(
            horizontal: MySize.size16,
            vertical: MySize.size12,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Cancel Button
        OutlinedButton(
          onPressed: () => Get.back(),
          style: OutlinedButton.styleFrom(
            side: BorderSide(color: AppColors.borderColor),
            padding: EdgeInsets.symmetric(
              horizontal: MySize.size24,
              vertical: MySize.size12,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
          ),
          child: Text(
            'Cancel',
            style: TextStyle(
              fontSize: MySize.size14,
              color: AppColors.textPrimary,
            ),
          ),
        ),

        SizedBox(width: MySize.size12),

        // Save Changes Button
        ElevatedButton(
          onPressed: _handleSave,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF4CAF50),
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(
              horizontal: MySize.size24,
              vertical: MySize.size12,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
          ),
          child: Text(
            'Save Changes',
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  void _handleSave() {
    // Create updated order with new priority and notes
    final updatedOrder = OrderModel(
      orderId: widget.order.orderId,
      customerName: widget.order.customerName,
      customerPhone: widget.order.customerPhone,
      orderDate: widget.order.orderDate,
      deliveryDate: widget.order.deliveryDate,
      itemCount: widget.order.itemCount,
      amount: widget.order.amount,
      status: widget.order.status,
      priority: selectedPriority,
      notes: notesController.text.trim().isEmpty ? null : notesController.text.trim(),
    );

    widget.onSave(updatedOrder);
  }
}
