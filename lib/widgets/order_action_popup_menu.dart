import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';

class OrderActionPopupMenu extends StatelessWidget {
  final VoidCallback onViewDetails;
  final VoidCallback onOrderStatus;
  final VoidCallback onEditOrder;
  final VoidCallback onDeleteOrder;

  const OrderActionPopupMenu({
    super.key,
    required this.onViewDetails,
    required this.onOrderStatus,
    required this.onEditOrder,
    required this.onDeleteOrder,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MySize.size200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: MySize.size16,
            offset: Offset(0, MySize.size4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildMenuItem(
            icon: 'visibility',
            label: 'View Details',
            onTap: onViewDetails,
          ),
          _buildDivider(),
          _buildMenuItem(
            icon: 'package',
            label: 'Order Status',
            onTap: onOrderStatus,
          ),
          _buildDivider(),
          _buildMenuItem(
            icon: 'edit',
            label: 'Edit Order',
            onTap: onEditOrder,
          ),
          _buildDivider(),
          _buildMenuItem(
            icon: 'delete',
            label: 'Delete Order',
            onTap: onDeleteOrder,
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required String icon,
    required String label,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(MySize.size12),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size16,
          vertical: MySize.size12,
        ),
        child: Row(
          children: [
            PlatformIcon(
              iconName: icon,
              size: MySize.size20,
              color: isDestructive ? Colors.red : AppColors.textSecondary,
            ),
            SizedBox(width: MySize.size12),
            Text(
              label,
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: isDestructive ? Colors.red : AppColors.textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 1,
      color: AppColors.borderColor.withValues(alpha: 0.3),
      indent: MySize.size16,
      endIndent: MySize.size16,
    );
  }
}
