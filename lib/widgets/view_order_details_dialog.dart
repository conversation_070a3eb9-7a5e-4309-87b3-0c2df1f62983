import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';

import '../models/order_model.dart';

class ViewOrderDetailsDialog extends StatelessWidget {
  final OrderModel order;

  const ViewOrderDetailsDialog({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    final isMobile = MediaQuery.of(context).size.width <= 768;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: isMobile ? MySize.size12 : MySize.size20,
        vertical: isMobile ? MySize.size20 : MySize.size40,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * (isMobile ? 0.95 : 0.9),
          maxWidth: isMobile ? double.infinity : MediaQuery.of(context).size.width * 0.8,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MySize.size16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(context),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(isMobile ? MySize.size16 : MySize.size24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Order Info Row
                    _buildOrderInfoRow(isMobile),

                    SizedBox(height: isMobile ? MySize.size16 : MySize.size24),

                    // Customer Information Section
                    _buildCustomerInformationSection(isMobile),

                    SizedBox(height: isMobile ? MySize.size16 : MySize.size24),

                    // Order Items Section
                    _buildOrderItemsSection(),

                    SizedBox(height: isMobile ? MySize.size16 : MySize.size24),

                    // Total Order Value
                    _buildTotalOrderValue(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(MySize.size16),
          topRight: Radius.circular(MySize.size16),
        ),
      ),
      child: Row(
        children: [
          PlatformIcon(
            iconName: 'package',
            size: MySize.size24,
            color: AppColors.primaryColor,
          ),
          SizedBox(width: MySize.size12),
          Expanded(
            child: Text(
              'Order Details - ${order.orderId}',
              style: TextStyle(
                fontSize: MySize.size18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              padding: EdgeInsets.all(MySize.size8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(MySize.size8),
              ),
              child: PlatformIcon(
                iconName: 'close',
                size: MySize.size16,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderInfoRow(bool isMobile) {
    if (isMobile) {
      return Column(
        children: [
          // First Column
          _buildInfoItem(
            icon: 'calendar',
            label: 'Order Date:',
            value: _formatDate(order.orderDate),
            iconColor: AppColors.primaryColor,
          ),
          SizedBox(height: MySize.size12),
          _buildInfoItem(
            icon: 'calendar',
            label: 'Delivery Date:',
            value: _formatDate(order.deliveryDate),
            iconColor: AppColors.primaryColor,
          ),
          SizedBox(height: MySize.size12),
          _buildInfoItem(
            icon: 'shopping_cart',
            label: 'Total Amount:',
            value: '₹ ${_formatAmount(order.amount)}',
            iconColor: AppColors.primaryColor,
          ),
          SizedBox(height: MySize.size12),
          _buildInfoItem(
            icon: 'shopping_cart',
            label: 'Payment Method:',
            value: 'Credit Card',
            iconColor: AppColors.primaryColor,
          ),
          SizedBox(height: MySize.size12),
          _buildInfoItem(
            icon: 'local_shipping',
            label: 'Tracking ID:',
            value: 'TRK${order.orderId.replaceAll('ORD-', '')}',
            iconColor: AppColors.primaryColor,
          ),
          SizedBox(height: MySize.size12),
          _buildStatusInfo(),
        ],
      );
    }

    return Row(
      children: [
        // Left Column
        Expanded(
          child: Column(
            children: [
              _buildInfoItem(
                icon: 'calendar',
                label: 'Order Date:',
                value: _formatDate(order.orderDate),
                iconColor: AppColors.primaryColor,
              ),
              SizedBox(height: MySize.size16),
              _buildInfoItem(
                icon: 'calendar',
                label: 'Delivery Date:',
                value: _formatDate(order.deliveryDate),
                iconColor: AppColors.primaryColor,
              ),
              SizedBox(height: MySize.size16),
              _buildStatusInfo(),
            ],
          ),
        ),
        SizedBox(width: MySize.size24),
        // Right Column
        Expanded(
          child: Column(
            children: [
              _buildInfoItem(
                icon: 'shopping_cart',
                label: 'Total Amount:',
                value: '₹ ${_formatAmount(order.amount)}',
                iconColor: AppColors.primaryColor,
              ),
              SizedBox(height: MySize.size16),
              _buildInfoItem(
                icon: 'shopping_cart',
                label: 'Payment Method:',
                value: 'Credit Card',
                iconColor: AppColors.primaryColor,
              ),
              SizedBox(height: MySize.size16),
              _buildInfoItem(
                icon: 'local_shipping',
                label: 'Tracking ID:',
                value: 'TRK${order.orderId.replaceAll('ORD-', '')}',
                iconColor: AppColors.primaryColor,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem({
    required String icon,
    required String label,
    required String value,
    required Color iconColor,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        PlatformIcon(
          iconName: icon,
          size: MySize.size16,
          color: iconColor,
        ),
        SizedBox(width: MySize.size8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: MySize.size12,
                  color: AppColors.textSecondary,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusInfo() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        PlatformIcon(
          iconName: 'package',
          size: MySize.size16,
          color: AppColors.primaryColor,
        ),
        SizedBox(width: MySize.size8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Status:',
                style: TextStyle(
                  fontSize: MySize.size12,
                  color: AppColors.textSecondary,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size8,
                  vertical: MySize.size4,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor(order.status).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(MySize.size12),
                ),
                child: Text(
                  order.status,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    fontWeight: FontWeight.w500,
                    color: _getStatusColor(order.status),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerInformationSection(bool isMobile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Customer Information',
          style: TextStyle(
            fontSize: MySize.size16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size16),
        if (isMobile) ...[
          // Mobile layout - stack vertically
          _buildCustomerInfoItem(
            icon: 'person',
            value: order.customerName,
          ),
          SizedBox(height: MySize.size12),
          _buildCustomerInfoItem(
            icon: 'phone',
            value: order.customerPhone,
          ),
          SizedBox(height: MySize.size12),
          _buildCustomerInfoItem(
            icon: 'email',
            value: '${order.customerName.toLowerCase().replaceAll(' ', '')}@gmail.com',
          ),
          SizedBox(height: MySize.size16),
          // Shipping Address
          Row(
            children: [
              PlatformIcon(
                iconName: 'location',
                size: MySize.size16,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Shipping Address',
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: MySize.size8),
          Text(
            'Venkat Nagar 4th Street Yanam\nPincode: 533464',
            style: TextStyle(
              fontSize: MySize.size14,
              color: AppColors.textPrimary,
            ),
          ),
        ] else ...[
          // Desktop layout - side by side
          Row(
            children: [
              // Left Column
              Expanded(
                child: Column(
                  children: [
                    _buildCustomerInfoItem(
                      icon: 'person',
                      value: order.customerName,
                    ),
                    SizedBox(height: MySize.size12),
                    _buildCustomerInfoItem(
                      icon: 'phone',
                      value: order.customerPhone,
                    ),
                    SizedBox(height: MySize.size12),
                    _buildCustomerInfoItem(
                      icon: 'email',
                      value: '${order.customerName.toLowerCase().replaceAll(' ', '')}@gmail.com',
                    ),
                  ],
                ),
              ),
              SizedBox(width: MySize.size24),
              // Right Column - Shipping Address
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        PlatformIcon(
                          iconName: 'location',
                          size: MySize.size16,
                          color: AppColors.primaryColor,
                        ),
                        SizedBox(width: MySize.size8),
                        Text(
                          'Shipping Address',
                          style: TextStyle(
                            fontSize: MySize.size14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: MySize.size8),
                    Text(
                      'Venkat Nagar 4th Street Yanam\nPincode: 533464',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildCustomerInfoItem({
    required String icon,
    required String value,
  }) {
    return Row(
      children: [
        PlatformIcon(
          iconName: icon,
          size: MySize.size16,
          color: AppColors.primaryColor,
        ),
        SizedBox(width: MySize.size8),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: MySize.size14,
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderItemsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            PlatformIcon(
              iconName: 'package',
              size: MySize.size20,
              color: AppColors.primaryColor,
            ),
            SizedBox(width: MySize.size8),
            Text(
              'Order Item (1)',
              style: TextStyle(
                fontSize: MySize.size16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        SizedBox(height: MySize.size16),
        Container(
          padding: EdgeInsets.all(MySize.size16),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.borderColor),
            borderRadius: BorderRadius.circular(MySize.size8),
          ),
          child: Row(
            children: [
              Container(
                width: MySize.size48,
                height: MySize.size48,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: PlatformIcon(
                  iconName: 'package',
                  size: MySize.size24,
                  color: AppColors.primaryColor,
                ),
              ),
              SizedBox(width: MySize.size12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Bond Paper Set',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      'Quantity: 3',
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '₹ ${_formatAmount(order.amount)}',
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTotalOrderValue() {
    return Container(
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: AppColors.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Total Order Value:',
            style: TextStyle(
              fontSize: MySize.size16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          Text(
            '₹ ${_formatAmount(order.amount)}',
            style: TextStyle(
              fontSize: MySize.size16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[date.month - 1]} ${date.day.toString().padLeft(2, '0')} ${date.year}';
  }

  String _formatAmount(double amount) {
    return amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'shipped':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'packing':
        return Colors.indigo;
      default:
        return AppColors.primaryColor;
    }
  }
}
