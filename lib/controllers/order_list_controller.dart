import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../models/order_model.dart';
import '../models/add_order_model.dart';
import '../services/firebase_service.dart';
import '../widgets/view_order_details_dialog.dart';
import '../widgets/delete_order_dialog.dart';
import '../widgets/edit_order_dialog.dart';

class OrderListController extends GetxController {
  // Services
  final FirebaseService _firebaseService = FirebaseService.instance;

  // Observable variables
  final RxList<OrderModel> orders = <OrderModel>[].obs;
  final RxList<OrderModel> filteredOrders = <OrderModel>[].obs;
  final RxString searchQuery = ''.obs;
  final RxInt selectedNavIndex = 2.obs; // Orders List is index 2
  final RxInt currentPage = 1.obs;
  final RxInt itemsPerPage = 10.obs;
  final RxBool isLoading = false.obs;

  // Controllers
  final TextEditingController searchController = TextEditingController();

  // Stream subscription
  StreamSubscription<List<AddOrderModel>>? _ordersSubscription;

  @override
  void onInit() {
    super.onInit();
    loadOrdersData();
  }

  @override
  void onClose() {
    _ordersSubscription?.cancel();
    searchController.dispose();
    super.onClose();
  }

  // Load orders data from Firebase
  void loadOrdersData() {
    isLoading.value = true;

    try {
      // Listen to orders stream from Firebase
      _ordersSubscription = _firebaseService.getOrdersStream().listen(
        (addOrderModels) {
          // Convert AddOrderModel to OrderModel for display
          final orderModels = addOrderModels.map((addOrder) => _convertToOrderModel(addOrder)).toList();

          orders.value = orderModels;
          filteredOrders.value = orderModels;
          isLoading.value = false;
        },
        onError: (error) {
          log('Error loading orders: $error');
          isLoading.value = false;
          Get.snackbar(
            'Error',
            'Failed to load orders. Please try again.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        },
      );
    } catch (e) {
      log('Error setting up orders stream: $e');
      isLoading.value = false;
      Get.snackbar(
        'Error',
        'Failed to load orders. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Convert AddOrderModel to OrderModel for display
  OrderModel _convertToOrderModel(AddOrderModel addOrder) {
    return OrderModel(
      orderId: addOrder.orderId,
      customerName: addOrder.customerName,
      customerPhone: addOrder.mobileNumber,
      orderDate: addOrder.createdAt ?? DateTime.now(),
      deliveryDate: addOrder.deliveryDate ?? DateTime.now().add(Duration(days: 10)),
      itemCount: addOrder.quantity,
      amount: addOrder.price * addOrder.quantity,
      status: addOrder.orderStatus,
      priority: addOrder.priority ?? 'No Priority',
      notes: addOrder.notes,
      productName: addOrder.productName, // Include product name
      productImageUrl: addOrder.productImageUrl, // Include product image URL
    );
  }

  // Handle search
  void onSearchChanged(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      filteredOrders.value = orders;
    } else {
      filteredOrders.value = orders.where((order) {
        return order.orderId.toLowerCase().contains(query.toLowerCase()) ||
               order.customerName.toLowerCase().contains(query.toLowerCase()) ||
               order.customerPhone.contains(query);
      }).toList();
    }
    currentPage.value = 1; // Reset to first page when searching
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List - already here
        break;
    }
  }

  // Handle export
  void onExportTap() {
    Get.snackbar(
      'Export',
      'Export functionality will be implemented soon',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Handle add order
  void onAddOrderTap() {
    Get.toNamed('/add-order');
  }

  // Handle order action menu
  void onOrderActionTap(OrderModel order) {
    // This method will be replaced by individual action methods
    // but kept for backward compatibility if needed
  }

  // Handle view order details
  void onViewOrderDetails(OrderModel order) {
    Get.dialog(
      ViewOrderDetailsDialog(order: order),
      barrierDismissible: true,
    );
  }

  // Handle order status navigation
  void onOrderStatus(OrderModel order) {
    Get.toNamed('/update-status', arguments: {
      'order': order,
      'sourceScreen': 'order-list',
    });
  }

  // Handle edit order
  void onEditOrder(OrderModel order) {
    _showEditOrderDialog(order);
  }

  // Handle delete order
  void onDeleteOrder(OrderModel order) {
    Get.dialog(
      DeleteOrderDialog(
        order: order,
        onConfirm: () => _confirmDeleteOrder(order),
      ),
      barrierDismissible: true,
    );
  }

  // Show Edit Order Dialog
  void _showEditOrderDialog(OrderModel order) {
    Get.dialog(
      EditOrderDialog(
        order: order,
        onSave: (updatedOrder) => _handleOrderUpdate(updatedOrder),
      ),
      barrierDismissible: false,
    );
  }

  // Handle order update after editing
  void _handleOrderUpdate(OrderModel updatedOrder) {
    // Find and update the order in the list
    final orderIndex = orders.indexWhere((o) => o.orderId == updatedOrder.orderId);
    if (orderIndex != -1) {
      orders[orderIndex] = updatedOrder;

      // Also update filtered orders if they contain this order
      final filteredIndex = filteredOrders.indexWhere((o) => o.orderId == updatedOrder.orderId);
      if (filteredIndex != -1) {
        filteredOrders[filteredIndex] = updatedOrder;
      }

      Get.back(); // Close dialog
      Get.snackbar(
        'Success',
        'Order ${updatedOrder.orderId} updated successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }
  }

  // Confirm delete order
  void _confirmDeleteOrder(OrderModel order) async {
    try {
      // Delete from Firebase
      final success = await _firebaseService.deleteOrder(order.orderId);

      if (success) {
        // Update pagination if needed
        if (paginatedOrders.isEmpty && currentPage.value > 1) {
          currentPage.value = currentPage.value - 1;
        }

        Get.snackbar(
          'Success',
          'Order ${order.orderId} deleted successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
      // Note: The orders list will be automatically updated via the stream
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to delete order. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Get paginated orders
  List<OrderModel> get paginatedOrders {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex = startIndex + itemsPerPage.value;

    if (startIndex >= filteredOrders.length) return [];

    return filteredOrders.sublist(
      startIndex,
      endIndex > filteredOrders.length ? filteredOrders.length : endIndex,
    );
  }

  // Get total pages
  int get totalPages {
    return (filteredOrders.length / itemsPerPage.value).ceil();
  }

  // Change page
  void changePage(int page) {
    if (page >= 1 && page <= totalPages) {
      currentPage.value = page;
    }
  }

  // Get status color (using exact Firebase status names)
  Color getStatusColor(String status) {
    switch (status) {
      case 'Packing':
        return const Color(0xFF3B82F6); // Blue
      case 'Design Plate Approved':
        return const Color(0xFF10B981); // Green
      case 'Sorting':
        return const Color(0xFFEC4899); // Pink
      case 'Cylinder Development':
        return const Color(0xFFF59E0B); // Orange
      case 'Heating':
        return const Color(0xFFEF4444); // Red
      case 'On-boarding':
        return const Color(0xFF06B6D4); // Cyan
      case 'Ready to Dispatch':
        return const Color(0xFF84CC16); // Lime
      case 'Designing':
        return const Color(0xFF9333EA); // Purple
      case 'Sampling':
        return const Color(0xFFEAB308); // Yellow
      case 'Polyester Sample Approved':
        return const Color(0xFF059669); // Emerald
      case 'Polyester Printing':
        return const Color(0xFFDC2626); // Red
      case 'Lamination':
        return const Color(0xFF7C3AED); // Violet
      case 'Metallised Pasting':
        return const Color(0xFF0891B2); // Sky
      case 'Curing':
        return const Color(0xFFEA580C); // Orange
      case 'Zipper Addition':
        return const Color(0xFFBE185D); // Pink
      case 'Slitting':
        return const Color(0xFF0D9488); // Teal
      case 'Pouching':
        return const Color(0xFF7C2D12); // Brown
      case 'Dispatched':
        return const Color(0xFF166534); // Green
      default:
        return const Color(0xFF6B7280); // Gray
    }
  }
}
