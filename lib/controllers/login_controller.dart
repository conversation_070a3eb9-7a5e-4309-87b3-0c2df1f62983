import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../routes/app_routes.dart';
import '../services/firebase_service.dart';

class LoginController extends GetxController {
  // Firebase service
  final FirebaseService _firebaseService = Get.find<FirebaseService>();

  // Text controllers
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  // Observable variables
  final agreeToPrivacyPolicy = false.obs;
  final isLoading = false.obs;
  final emailText = ''.obs;
  final passwordText = ''.obs;

  // Form validation
  final formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    // Listen to text changes
    emailController.addListener(() {
      emailText.value = emailController.text;
    });
    passwordController.addListener(() {
      passwordText.value = passwordController.text;
    });
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  // Toggle privacy policy agreement
  void togglePrivacyPolicy(bool? value) {
    agreeToPrivacyPolicy.value = value ?? false;
  }

  // Validate form
  bool get isFormValid {
    return emailText.value.isNotEmpty &&
           passwordText.value.isNotEmpty &&
           agreeToPrivacyPolicy.value &&
           _firebaseService.isValidEmail(emailText.value);
  }

  // Handle login
  Future<void> handleLogin() async {
    if (!isFormValid) {
      String errorMessage = 'Please fill all fields and agree to privacy policy';

      if (emailText.value.isNotEmpty && !_firebaseService.isValidEmail(emailText.value)) {
        errorMessage = 'Please enter a valid email address';
      } else if (passwordText.value.isNotEmpty && !_firebaseService.isValidPassword(passwordText.value)) {
        errorMessage = 'Password must be at least 6 characters long';
      }

      Get.snackbar(
        'Validation Error',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isLoading.value = true;

      // Sign in with Firebase
      final userCredential = await _firebaseService.signInWithEmailAndPassword(
        email: emailController.text,
        password: passwordController.text,
      );

      if (userCredential != null) {
        // Show success message
        Get.snackbar(
          'Success',
          'Login successful! Welcome back.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Navigate to dashboard
        Get.offAllNamed(AppRoutes.dashboard);
      }
    } catch (e) {
      // Error handling is done in FirebaseService
    } finally {
      isLoading.value = false;
    }
  }

  // Handle forgot password
  Future<void> handleForgotPassword() async {
    if (emailText.value.isEmpty) {
      Get.snackbar(
        'Email Required',
        'Please enter your email address first',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    if (!_firebaseService.isValidEmail(emailText.value)) {
      Get.snackbar(
        'Invalid Email',
        'Please enter a valid email address',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    await _firebaseService.sendPasswordResetEmail(email: emailController.text);
  }

  // Handle sign up navigation
  void handleSignUp() {
    Get.snackbar(
      'Info',
      'Sign up functionality will be implemented soon',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }
}
