import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../models/customer_model.dart';
import '../models/customer_profile_model.dart';
import '../models/order_model.dart';
import '../models/add_customer_model.dart';
import '../models/add_order_model.dart';
import '../services/firebase_service.dart';

class CustomerProfileController extends GetxController {
  // Services
  final FirebaseService _firebaseService = FirebaseService.instance;

  // Observable variables
  final RxInt selectedNavIndex = 1.obs; // Customer Profile is accessed from Customers List (index 1)
  final RxBool isLoading = false.obs;
  final Rx<CustomerProfileModel?> customerProfile = Rx<CustomerProfileModel?>(null);
  final Rx<AddCustomerModel?> currentCustomer = Rx<AddCustomerModel?>(null);
  final RxList<OrderModel> customerOrders = <OrderModel>[].obs;
  final RxList<OrderProgressStep> orderProgressSteps = <OrderProgressStep>[].obs;
  final RxString notes = ''.obs;
  final RxBool isEditingNotes = false.obs;

  // Controllers
  final TextEditingController notesController = TextEditingController();

  // Stream subscriptions
  StreamSubscription<List<AddOrderModel>>? _ordersSubscription;

  @override
  void onInit() {
    super.onInit();
    final arguments = Get.arguments;
    if (arguments != null && arguments['customer'] != null) {
      final CustomerModel customer = arguments['customer'];
      loadCustomerProfile(customer);
    }
  }

  @override
  void onClose() {
    _ordersSubscription?.cancel();
    notesController.dispose();
    super.onClose();
  }

  // Load customer profile data from Firebase
  void loadCustomerProfile(CustomerModel customer) async {
    isLoading.value = true;

    try {
      // Get real customer data from Firebase by phone number
      final firebaseCustomer = await _firebaseService.getCustomerByPhone(customer.phoneNumber);

      if (firebaseCustomer != null) {
        currentCustomer.value = firebaseCustomer;

        // Create initial customer profile with Firebase data
        final profile = CustomerProfileModel(
          name: firebaseCustomer.fullName,
          phoneNumber: firebaseCustomer.mobileNumber,
          email: firebaseCustomer.emailId,
          location: firebaseCustomer.businessAddress.isNotEmpty
            ? firebaseCustomer.businessAddress
            : 'Not specified',
          businessName: firebaseCustomer.businessName,
          gstNumber: firebaseCustomer.gstNumber,
          businessAddress: firebaseCustomer.businessAddress,
          totalOrders: 0, // Will be updated when orders are loaded
          totalSpent: 0.0, // Will be updated when orders are loaded
          avgOrderValue: 0.0, // Will be updated when orders are loaded
          currentStatus: 'No orders', // Will be updated when orders are loaded
          notes: firebaseCustomer.notes,
          contactLog: [], // Real contact log can be implemented later
          lastOrderDate: null, // Will be updated when orders are loaded
        );

        customerProfile.value = profile;
        notes.value = profile.notes ?? '';
        notesController.text = notes.value;

        // Load customer orders from Firebase (this will update the profile stats automatically)
        await loadCustomerOrders(customer.phoneNumber);
      } else {
        // Customer not found in Firebase, show error
        Get.snackbar(
          'Error',
          'Customer data not found in database.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      log('Error loading customer profile: $e');
      Get.snackbar(
        'Error',
        'Failed to load customer profile. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }

    isLoading.value = false;
  }

  // Load customer orders from Firebase
  Future<void> loadCustomerOrders(String customerPhone) async {
    try {
      // Listen to orders stream and filter by customer phone or customerId
      _ordersSubscription = _firebaseService.getOrdersStream().listen(
        (addOrderModels) {
          // Filter orders for this customer by customerId (preferred) or phone number (fallback)
          final customerAddOrders = addOrderModels.where((order) {
            // If order has customerId and current customer has customerId, match by customerId
            if (order.customerId != null && currentCustomer.value?.customerId != null) {
              return order.customerId == currentCustomer.value!.customerId;
            }
            // Otherwise, fall back to phone number matching
            return order.mobileNumber == customerPhone;
          }).toList();

          // Convert AddOrderModel to OrderModel for display
          final orderModels = customerAddOrders.map((addOrder) => OrderModel(
            orderId: addOrder.orderId,
            customerName: addOrder.customerName,
            customerPhone: addOrder.mobileNumber,
            orderDate: addOrder.createdAt ?? DateTime.now(),
            deliveryDate: addOrder.deliveryDate ?? DateTime.now().add(Duration(days: 10)),
            itemCount: addOrder.quantity,
            amount: addOrder.price * addOrder.quantity,
            status: addOrder.orderStatus,
            priority: addOrder.priority ?? 'No Priority',
            notes: addOrder.notes,
            productName: addOrder.productName, // Include product name
          )).toList();

          // Sort by order date (newest first)
          orderModels.sort((a, b) => b.orderDate.compareTo(a.orderDate));

          customerOrders.value = orderModels;

          // Update customer profile with latest order statistics
          _updateCustomerProfileStats();
        },
        onError: (error) {
          log('Error loading customer orders: $error');
        },
      );
    } catch (e) {
      log('Error setting up customer orders stream: $e');
    }
  }

  // Update customer profile statistics when orders change
  void _updateCustomerProfileStats() {
    if (customerProfile.value != null && customerOrders.isNotEmpty) {
      final customerOrdersList = customerOrders.toList();
      final totalSpent = customerOrdersList.fold<double>(0.0, (sum, order) => sum + order.amount);
      final avgOrderValue = customerOrdersList.isNotEmpty ? totalSpent / customerOrdersList.length : 0.0;
      final lastOrder = customerOrdersList.isNotEmpty ? customerOrdersList.first : null;

      // Update the customer profile with new statistics
      final updatedProfile = CustomerProfileModel(
        name: customerProfile.value!.name,
        phoneNumber: customerProfile.value!.phoneNumber,
        email: customerProfile.value!.email,
        location: customerProfile.value!.location,
        businessName: customerProfile.value!.businessName,
        gstNumber: customerProfile.value!.gstNumber,
        businessAddress: customerProfile.value!.businessAddress,
        totalOrders: customerOrdersList.length,
        totalSpent: totalSpent,
        avgOrderValue: avgOrderValue,
        currentStatus: lastOrder?.status ?? 'No orders',
        notes: customerProfile.value!.notes,
        contactLog: customerProfile.value!.contactLog,
        lastOrderDate: lastOrder?.orderDate,
      );

      customerProfile.value = updatedProfile;

      // Update order progress for most recent order
      if (lastOrder != null) {
        loadOrderProgress(lastOrder.status);
      }
    }
  }

  // Load order progress timeline based on current status
  void loadOrderProgress([String? currentStatus]) {
    if (currentStatus == null || currentStatus == 'No orders') {
      orderProgressSteps.value = [];
      return;
    }

    // Define the order process flow (matching the order status options from add_order)
    final allSteps = [
      'Packing',
      'On-boarding',
      'Designing',
      'Sampling',
      'Design Plate Approved',
      'Cylinder Development',
      'Polyester Sample Approved',
      'Polyester Printing',
      'Lamination',
      'Metallised Pasting',
      'Heating',
      'Curing',
      'Zipper Addition',
      'Slitting',
      'Pouching',
      'Sorting',
      'Ready to Dispatch',
      'Dispatched',
    ];

    final currentIndex = allSteps.indexOf(currentStatus);
    final steps = <OrderProgressStep>[];

    for (int i = 0; i < allSteps.length; i++) {
      final stepTitle = allSteps[i];
      final isCompleted = i < currentIndex;
      final isActive = i == currentIndex;

      // Only show steps up to current + 2 future steps for better UI
      if (i <= currentIndex + 2) {
        steps.add(OrderProgressStep(
          title: stepTitle,
          date: isCompleted || isActive ? _formatDate(DateTime.now().subtract(Duration(days: currentIndex - i))) : '',
          isCompleted: isCompleted,
          isActive: isActive,
        ));
      }
    }

    orderProgressSteps.value = steps;
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    final months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle edit notes
  void onEditNotes() {
    isEditingNotes.value = true;
  }

  // Handle save notes
  void onSaveNotes() async {
    if (currentCustomer.value == null) {
      Get.snackbar(
        'Error',
        'Customer data not found',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // Update notes in Firebase
      final success = await _firebaseService.updateCustomerNotes(
        currentCustomer.value!.customerId,
        notesController.text,
      );

      if (success) {
        notes.value = notesController.text;
        if (customerProfile.value != null) {
          customerProfile.value = customerProfile.value!.copyWith(notes: notes.value);
        }
        isEditingNotes.value = false;

        Get.snackbar(
          'Success',
          'Notes updated successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update notes. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Handle cancel edit notes
  void onCancelEditNotes() {
    notesController.text = notes.value;
    isEditingNotes.value = false;
  }

  // Handle add contact log entry
  void onAddContactLog() {
    Get.snackbar(
      'Add Contact',
      'Add contact log functionality will be implemented soon',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Get recent orders (top 4)
  List<OrderModel> get recentOrders {
    return customerOrders.take(4).toList();
  }

  // Get status color (using exact Firebase status names)
  Color getStatusColor(String status) {
    switch (status) {
      case 'Packing':
        return const Color(0xFF2196F3);
      case 'Design Plate Approved':
        return const Color(0xFF4CAF50);
      case 'Designing':
        return const Color(0xFFFF9800);
      case 'Heating':
        return const Color(0xFFF44336);
      case 'On-boarding':
        return const Color(0xFF06B6D4);
      case 'Sampling':
        return const Color(0xFFEAB308);
      case 'Cylinder Development':
        return const Color(0xFFF59E0B);
      case 'Polyester Sample Approved':
        return const Color(0xFF059669);
      case 'Polyester Printing':
        return const Color(0xFFDC2626);
      case 'Lamination':
        return const Color(0xFF7C3AED);
      case 'Metallised Pasting':
        return const Color(0xFF0891B2);
      case 'Curing':
        return const Color(0xFFEA580C);
      case 'Zipper Addition':
        return const Color(0xFFBE185D);
      case 'Slitting':
        return const Color(0xFF0D9488);
      case 'Pouching':
        return const Color(0xFF7C2D12);
      case 'Sorting':
        return const Color(0xFFEC4899);
      case 'Ready to Dispatch':
        return const Color(0xFF84CC16);
      case 'Dispatched':
        return const Color(0xFF166534);
      default:
        return const Color(0xFF9E9E9E);
    }
  }
}
