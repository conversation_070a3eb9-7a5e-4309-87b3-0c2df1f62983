import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/order_model.dart';
import '../services/firebase_service.dart';

class UpdateStatusController extends GetxController {
  // Services
  final FirebaseService _firebaseService = FirebaseService.instance;

  // Observable variables
  final RxInt selectedNavIndex = 0.obs; // Default to Dashboard
  final RxBool isLoading = false.obs;
  final RxString selectedOrderStatus = 'Pouching'.obs;
  final Rx<OrderModel?> currentOrder = Rx<OrderModel?>(null);
  final RxString sourceScreen = 'dashboard'.obs; // Track where user came from

  // Controllers
  final TextEditingController remarksController = TextEditingController();

  // Order status options
  final List<String> orderStatusOptions = [
    'On-boarding',
    'Designing',
    'Sampling',
    'Design Plate Approved',
    'Cylinder Development',
    'Polyester Sample Approved',
    'Polyester Printing',
    'Lamination',
    'Metallised Pasting',
    'Heating',
    'Curing',
    'Zipper Addition',
    'Slitting',
    'Pouching',
    'Sorting',
    'Packing',
    'Ready to Dispatch',
    'Dispatched'
  ];

  // All possible status progress steps
  final List<Map<String, dynamic>> allStatusSteps = [
    {
      'title': 'On-boarding',
      'icon': 'check_circle',
    },
    {
      'title': 'Designing',
      'icon': 'design_services',
    },
    {
      'title': 'Sampling',
      'icon': 'science',
    },
    {
      'title': 'Design Plate Approved',
      'icon': 'verified',
    },
    {
      'title': 'Cylinder Development',
      'icon': 'settings',
    },
    {
      'title': 'Polyester Sample Approved',
      'icon': 'verified',
    },
    {
      'title': 'Polyester Printing',
      'icon': 'print',
    },
    {
      'title': 'Lamination',
      'icon': 'layers',
    },
    {
      'title': 'Metallised Pasting',
      'icon': 'build',
    },
    {
      'title': 'Heating',
      'icon': 'local_fire_department',
    },
    {
      'title': 'Curing',
      'icon': 'timer',
    },
    {
      'title': 'Zipper Addition',
      'icon': 'add',
    },
    {
      'title': 'Slitting',
      'icon': 'content_cut',
    },
    {
      'title': 'Pouching',
      'icon': 'inventory',
    },
    {
      'title': 'Sorting',
      'icon': 'sort',
    },
    {
      'title': 'Packing',
      'icon': 'package',
    },
    {
      'title': 'Ready to Dispatch',
      'icon': 'local_shipping',
    },
    {
      'title': 'Dispatched',
      'icon': 'done_all',
    },
  ];

  // Dynamic status progress based on current order status
  final RxList<Map<String, dynamic>> statusProgress = <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();

    // Get order data from route arguments
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      if (arguments['order'] != null) {
        currentOrder.value = arguments['order'] as OrderModel;
        selectedOrderStatus.value = currentOrder.value?.status ?? 'Pouching';
      }

      // Set navigation index based on source screen
      if (arguments['sourceScreen'] != null) {
        sourceScreen.value = arguments['sourceScreen'];
        selectedNavIndex.value = arguments['sourceScreen'] == 'dashboard' ? 0 : 2;
      }
    }

    // Generate dynamic status progress based on current order status
    _generateStatusProgress();

    // Initialize remarks with sample text
    remarksController.text = 'Paper trimmed to required size, edges are clean';
  }

  @override
  void onClose() {
    remarksController.dispose();
    super.onClose();
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle order status change
  void onOrderStatusChanged(String? newStatus) {
    if (newStatus != null) {
      selectedOrderStatus.value = newStatus;

      // Update the current order status temporarily for preview
      if (currentOrder.value != null) {
        currentOrder.value = OrderModel(
          orderId: currentOrder.value!.orderId,
          customerName: currentOrder.value!.customerName,
          customerPhone: currentOrder.value!.customerPhone,
          orderDate: currentOrder.value!.orderDate,
          deliveryDate: currentOrder.value!.deliveryDate,
          itemCount: currentOrder.value!.itemCount,
          amount: currentOrder.value!.amount,
          status: newStatus,
          priority: currentOrder.value!.priority,
          notes: currentOrder.value!.notes,
          productName: currentOrder.value!.productName,
          productImageUrl: currentOrder.value!.productImageUrl,
        );

        // Regenerate status progress with new status
        _generateStatusProgress();
      }
    }
  }

  // Generate status progress based on current order status
  void _generateStatusProgress() {
    if (currentOrder.value == null) return;

    String currentStatus = currentOrder.value!.status;
    int currentStatusIndex = orderStatusOptions.indexOf(currentStatus);

    List<Map<String, dynamic>> progress = [];

    // Show completed steps up to current status
    for (int i = 0; i <= currentStatusIndex && i < allStatusSteps.length; i++) {
      progress.add({
        'title': allStatusSteps[i]['title'],
        'date': '4 June 2025',
        'time': '${9 + i}:30 ${i % 2 == 0 ? 'Am' : 'Pm'}',
        'isCompleted': true,
        'icon': allStatusSteps[i]['icon'],
      });
    }

    // Show next 2-3 upcoming steps as incomplete
    for (int i = currentStatusIndex + 1; i < currentStatusIndex + 3 && i < allStatusSteps.length; i++) {
      progress.add({
        'title': allStatusSteps[i]['title'],
        'date': 'Pending',
        'time': 'Pending',
        'isCompleted': false,
        'icon': allStatusSteps[i]['icon'],
      });
    }

    statusProgress.value = progress;
  }

  // Handle update order status
  Future<void> onUpdateOrderStatus() async {
    if (currentOrder.value == null) {
      Get.snackbar(
        'Error',
        'No order data found',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isLoading.value = true;

      final orderId = currentOrder.value!.orderId;
      final newStatus = selectedOrderStatus.value;
      final remarks = remarksController.text.trim();

      // Update order status and notes in Firebase
      final success = await _firebaseService.updateOrderStatusAndNotes(
        orderId,
        newStatus,
        remarks.isNotEmpty ? remarks : null,
      );

      if (success) {
        // Update the order status locally
        if (currentOrder.value != null) {
          currentOrder.value = OrderModel(
            orderId: currentOrder.value!.orderId,
            customerName: currentOrder.value!.customerName,
            customerPhone: currentOrder.value!.customerPhone,
            orderDate: currentOrder.value!.orderDate,
            deliveryDate: currentOrder.value!.deliveryDate,
            itemCount: currentOrder.value!.itemCount,
            amount: currentOrder.value!.amount,
            status: newStatus,
            priority: currentOrder.value!.priority,
            notes: remarks.isNotEmpty ? remarks : currentOrder.value!.notes,
            productName: currentOrder.value!.productName,
          );

          // Regenerate status progress with new status
          _generateStatusProgress();
        }

        Get.snackbar(
          'Success',
          'Order status updated successfully to $newStatus',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Navigate back after successful update with updated order data
        Get.back(result: {
          'updatedOrder': currentOrder.value,
          'wasUpdated': true,
        });
      }
    } catch (e) {
      log('Error updating order status: $e');
      Get.snackbar(
        'Error',
        'Failed to update order status. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Format date for display
  String formatDate(DateTime date) {
    return '${_getMonthName(date.month)} ${date.day}, ${date.year}';
  }

  String _getMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }

  // Get status color (using exact Firebase status names)
  Color getStatusColor(String status) {
    switch (status) {
      case 'Pouching':
        return const Color(0xFF8B5CF6); // Purple
      case 'Packing':
        return const Color(0xFF3B82F6); // Blue
      case 'Design Plate Approved':
        return const Color(0xFF10B981); // Green
      case 'Sorting':
        return const Color(0xFFEC4899); // Pink
      case 'Cylinder Development':
        return const Color(0xFFF59E0B); // Orange
      case 'Heating':
        return const Color(0xFFEF4444); // Red
      case 'On-boarding':
        return const Color(0xFF06B6D4); // Cyan
      case 'Ready to Dispatch':
        return const Color(0xFF84CC16); // Lime
      case 'Designing':
        return const Color(0xFF9333EA); // Purple
      case 'Sampling':
        return const Color(0xFFEAB308); // Yellow
      case 'Polyester Sample Approved':
        return const Color(0xFF059669); // Emerald
      case 'Polyester Printing':
        return const Color(0xFFDC2626); // Red
      case 'Lamination':
        return const Color(0xFF7C3AED); // Violet
      case 'Metallised Pasting':
        return const Color(0xFF0891B2); // Sky
      case 'Curing':
        return const Color(0xFFEA580C); // Orange
      case 'Zipper Addition':
        return const Color(0xFFBE185D); // Pink
      case 'Slitting':
        return const Color(0xFF0D9488); // Teal
      case 'Dispatched':
        return const Color(0xFF166534); // Green
      default:
        return const Color(0xFF6B7280); // Gray
    }
  }
}
