import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../models/add_order_model.dart';
import '../services/firebase_service.dart';

class DashboardController extends GetxController {
  // Services
  final FirebaseService _firebaseService = FirebaseService.instance;

  // Search controller
  final searchController = TextEditingController();

  // Observable variables
  final searchQuery = ''.obs;
  final selectedNavIndex = 0.obs;
  final isLoading = false.obs;

  // Dashboard data
  final dashboardData = <String, dynamic>{}.obs;
  final RxList<AddOrderModel> allOrders = <AddOrderModel>[].obs;
  final RxList<AddOrderModel> filteredOrders = <AddOrderModel>[].obs;

  // Stream subscription
  StreamSubscription<List<AddOrderModel>>? _ordersSubscription;

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();

    // Listen to search changes
    searchController.addListener(() {
      searchQuery.value = searchController.text;
    });
  }

  @override
  void onClose() {
    _ordersSubscription?.cancel();
    searchController.dispose();
    super.onClose();
  }

  // Load dashboard data from Firebase
  void loadDashboardData() {
    isLoading.value = true;

    try {
      // Listen to orders stream from Firebase
      _ordersSubscription = _firebaseService.getOrdersStream().listen(
        (orders) {
          allOrders.value = orders;
          _calculateDashboardData(orders);
          _applySearchFilter();
          isLoading.value = false;
        },
        onError: (error) {
          log('Error loading dashboard data: $error');
          isLoading.value = false;
          Get.snackbar(
            'Error',
            'Failed to load dashboard data. Please try again.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        },
      );
    } catch (e) {
      log('Error setting up dashboard stream: $e');
      isLoading.value = false;
      Get.snackbar(
        'Error',
        'Failed to load dashboard data. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Calculate dashboard data from orders
  void _calculateDashboardData(List<AddOrderModel> orders) {
    final statusCounts = <String, int>{};

    // Count orders by status (using exact Firebase status names)
    for (final order in orders) {
      final status = order.orderStatus; // Use exact status name from Firebase
      statusCounts[status] = (statusCounts[status] ?? 0) + 1;
    }

    // Map status names to dashboard keys (using exact Firebase status names)
    dashboardData.value = {
      'totalOrders': orders.length,
      'onBoarding': statusCounts['On-boarding'] ?? 0,
      'designing': statusCounts['Designing'] ?? 0,
      'sampling': statusCounts['Sampling'] ?? 0,
      'designPlateApproved': statusCounts['Design Plate Approved'] ?? 0,
      'cylinderDevelopment': statusCounts['Cylinder Development'] ?? 0,
      'polyesterSampleApproved': statusCounts['Polyester Sample Approved'] ?? 0,
      'polyesterPrinting': statusCounts['Polyester Printing'] ?? 0,
      'lamination': statusCounts['Lamination'] ?? 0,
      'metallisedPasting': statusCounts['Metallised Pasting'] ?? 0,
      'heating': statusCounts['Heating'] ?? 0,
      'curing': statusCounts['Curing'] ?? 0,
      'zipperAddition': statusCounts['Zipper Addition'] ?? 0,
      'slitting': statusCounts['Slitting'] ?? 0,
      'pouching': statusCounts['Pouching'] ?? 0,
      'sorting': statusCounts['Sorting'] ?? 0,
      'packing': statusCounts['Packing'] ?? 0,
      'readyToDispatch': statusCounts['Ready to Dispatch'] ?? 0,
      'dispatched': statusCounts['Dispatched'] ?? 0,
    };
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard - already here
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle search
  void onSearchChanged(String query) {
    searchQuery.value = query;
    _applySearchFilter();
  }

  // Apply search filter to orders
  void _applySearchFilter() {
    if (searchQuery.value.isEmpty) {
      filteredOrders.value = allOrders;
    } else {
      filteredOrders.value = allOrders.where((order) {
        return order.orderId.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
               order.customerName.toLowerCase().contains(searchQuery.value.toLowerCase()) ||
               order.mobileNumber.contains(searchQuery.value) ||
               order.orderStatus.toLowerCase().contains(searchQuery.value.toLowerCase());
      }).toList();
    }
  }

  // Get filtered orders for search results
  List<AddOrderModel> get searchResults => filteredOrders;

  // Handle card tap
  void onCardTap(String cardType, int count) {
    // Navigate to dashboard card detail screen
    Get.toNamed('/dashboard-card-detail', arguments: {
      'title': cardType,
      'count': count,
    });
  }

  // Get dashboard items with real data (only non-zero counts)
  List<Map<String, dynamic>> getDashboardItems() {
    final items = [
      {
        'title': 'Total Orders',
        'count': dashboardData['totalOrders'] ?? 0,
        'iconPath': 'assets/icons/onboarding_icon.png',
        'iconColor': Colors.black,
        'key': 'totalOrders',
      },
      {
        'title': 'On-boarding',
        'count': dashboardData['onBoarding'] ?? 0,
        'iconPath': 'assets/icons/onboarding_icon.png',
        'iconColor': Colors.black,
        'key': 'onBoarding',
      },
      {
        'title': 'Designing',
        'count': dashboardData['designing'] ?? 0,
        'iconPath': 'assets/icons/designing_icon.png',
        'iconColor': Colors.black,
        'key': 'designing',
      },
      {
        'title': 'Sampling',
        'count': dashboardData['sampling'] ?? 0,
        'iconPath': 'assets/icons/sampling_icon.png',
        'iconColor': Colors.black,
        'key': 'sampling',
      },
      {
        'title': 'Design Plate Approved',
        'count': dashboardData['designPlateApproved'] ?? 0,
        'iconPath': 'assets/icons/design_plate_approval_icon.png',
        'iconColor': Colors.black,
        'key': 'designPlateApproved',
      },
      {
        'title': 'Cylinder Development',
        'count': dashboardData['cylinderDevelopment'] ?? 0,
        'iconPath': 'assets/icons/cylinder_icon.png',
        'iconColor': Colors.black,
        'key': 'cylinderDevelopment',
      },
      {
        'title': 'Polyester Sample Approved',
        'count': dashboardData['polyesterSampleApproved'] ?? 0,
        'iconPath': 'assets/icons/polyster_sample_approved_icon.png',
        'iconColor': Colors.black,
        'key': 'polyesterSampleApproved',
      },
      {
        'title': 'Polyester Printing',
        'count': dashboardData['polyesterPrinting'] ?? 0,
        'iconPath': 'assets/icons/polyster_printing_icon.png',
        'iconColor': Colors.black,
        'key': 'polyesterPrinting',
      },
      {
        'title': 'Lamination',
        'count': dashboardData['lamination'] ?? 0,
        'iconPath': 'assets/icons/lamination_icon.png',
        'iconColor': Colors.black,
        'key': 'lamination',
      },
      {
        'title': 'Metallised Pasting',
        'count': dashboardData['metallisedPasting'] ?? 0,
        'iconPath': 'assets/icons/pasting_icon.png',
        'iconColor': Colors.black,
        'key': 'metallisedPasting',
      },
      {
        'title': 'Heating',
        'count': dashboardData['heating'] ?? 0,
        'iconPath': 'assets/icons/heating_icon.png',
        'iconColor': Colors.black,
        'key': 'heating',
      },
      {
        'title': 'Curing',
        'count': dashboardData['curing'] ?? 0,
        'iconPath': 'assets/icons/curing_icon.png',
        'iconColor': Colors.black,
        'key': 'curing',
      },
      {
        'title': 'Zipper Addition',
        'count': dashboardData['zipperAddition'] ?? 0,
        'iconPath': 'assets/icons/zipper_addition_icon.png',
        'iconColor': Colors.black,
        'key': 'zipperAddition',
      },
      {
        'title': 'Slitting',
        'count': dashboardData['slitting'] ?? 0,
        'iconPath': 'assets/icons/slitting_icon.png',
        'iconColor': Colors.black,
        'key': 'slitting',
      },
      {
        'title': 'Pouching',
        'count': dashboardData['pouching'] ?? 0,
        'iconPath': 'assets/icons/pouching_icon.png',
        'iconColor': Colors.black,
        'key': 'pouching',
      },
      {
        'title': 'Sorting',
        'count': dashboardData['sorting'] ?? 0,
        'iconPath': 'assets/icons/sorting_icon.png',
        'iconColor': Colors.black,
        'key': 'sorting',
      },
      {
        'title': 'Packing',
        'count': dashboardData['packing'] ?? 0,
        'iconPath': 'assets/icons/packing_icon.png',
        'iconColor': Colors.black,
        'key': 'packing',
      },
      {
        'title': 'Ready to Dispatch',
        'count': dashboardData['readyToDispatch'] ?? 0,
        'iconPath': 'assets/icons/ready_to_dispatch_icon.png',
        'iconColor': Colors.black,
        'key': 'readyToDispatch',
      },
      {
        'title': 'Dispatched',
        'count': dashboardData['dispatched'] ?? 0,
        'iconPath': 'assets/icons/dispatched_icon.png',
        'iconColor': Colors.black,
        'key': 'dispatched',
      },
    ];

    // Filter out items with zero count (except Total Orders)
    return items.where((item) {
      return item['key'] == 'totalOrders' || (item['count'] as int) > 0;
    }).toList();
  }
}
