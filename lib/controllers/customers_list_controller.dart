import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import '../models/customer_model.dart';
import '../models/add_customer_model.dart';
import '../models/add_order_model.dart';
import '../services/firebase_service.dart';

class CustomersListController extends GetxController {
  // Services
  final FirebaseService _firebaseService = FirebaseService.instance;

  // Observable variables
  final RxList<CustomerModel> customers = <CustomerModel>[].obs;
  final RxList<CustomerModel> filteredCustomers = <CustomerModel>[].obs;
  final RxString searchQuery = ''.obs;
  final RxInt selectedNavIndex = 1.obs; // Customers List is index 1
  final RxInt currentPage = 1.obs;
  final RxInt itemsPerPage = 10.obs;
  final RxBool isLoading = false.obs;

  // Controllers
  final TextEditingController searchController = TextEditingController();

  // Stream subscriptions
  StreamSubscription<List<AddCustomerModel>>? _customersSubscription;
  StreamSubscription<List<AddOrderModel>>? _ordersSubscription;

  @override
  void onInit() {
    super.onInit();
    loadCustomersData();
  }

  @override
  void onClose() {
    _customersSubscription?.cancel();
    _ordersSubscription?.cancel();
    searchController.dispose();
    super.onClose();
  }

  // Load customers data from Firebase
  void loadCustomersData() {
    isLoading.value = true;

    try {
      // Listen to customers stream from Firebase
      _customersSubscription = _firebaseService.getCustomersStream().listen(
        (addCustomerModels) async {
          // Get orders to calculate order counts
          final orders = await _firebaseService.getOrders();

          // Convert AddCustomerModel to CustomerModel for display
          final customerModels = addCustomerModels.map((addCustomer) =>
            _convertToCustomerModel(addCustomer, orders)).toList();

          customers.value = customerModels;
          filteredCustomers.value = customerModels;
          isLoading.value = false;
        },
        onError: (error) {
          log('Error loading customers: $error');
          isLoading.value = false;
          Get.snackbar(
            'Error',
            'Failed to load customers. Please try again.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        },
      );

      // Also listen to orders stream to update customer order counts in real-time
      _ordersSubscription = _firebaseService.getOrdersStream().listen(
        (orders) {
          // Update customer order counts when orders change
          if (customers.isNotEmpty) {
            final updatedCustomers = customers.map((customer) {
              final orderCount = orders.where((order) =>
                order.mobileNumber == customer.phoneNumber).length;
              return CustomerModel(
                name: customer.name,
                phoneNumber: customer.phoneNumber,
                location: customer.location,
                orderCount: orderCount,
              );
            }).toList();

            customers.value = updatedCustomers;
            // Update filtered customers if search is active
            if (searchQuery.value.isNotEmpty) {
              onSearchChanged(searchQuery.value);
            } else {
              filteredCustomers.value = updatedCustomers;
            }
          }
        },
      );
    } catch (e) {
      log('Error setting up customers stream: $e');
      isLoading.value = false;
      Get.snackbar(
        'Error',
        'Failed to load customers. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Convert AddCustomerModel to CustomerModel for display
  CustomerModel _convertToCustomerModel(AddCustomerModel addCustomer, List<AddOrderModel> orders) {
    // Calculate order count for this customer
    final orderCount = orders.where((order) =>
      order.mobileNumber == addCustomer.mobileNumber).length;

    return CustomerModel(
      name: addCustomer.fullName,
      phoneNumber: addCustomer.mobileNumber,
      location: addCustomer.businessAddress.isNotEmpty
        ? addCustomer.businessAddress
        : 'Not specified',
      orderCount: orderCount,
    );
  }

  // Handle search
  void onSearchChanged(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      filteredCustomers.value = customers;
    } else {
      filteredCustomers.value = customers.where((customer) {
        return customer.name.toLowerCase().contains(query.toLowerCase()) ||
               customer.phoneNumber.contains(query) ||
               customer.location.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }
    currentPage.value = 1; // Reset to first page when searching
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List - already here
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle add customer
  void onAddCustomerTap() {
    Get.toNamed('/add-customer');
  }

  // Handle customer action (view)
  void onCustomerActionTap(CustomerModel customer) {
    Get.toNamed('/customer-profile', arguments: {
      'customer': customer,
    });
  }

  // Get paginated customers
  List<CustomerModel> get paginatedCustomers {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex = startIndex + itemsPerPage.value;

    if (startIndex >= filteredCustomers.length) return [];

    return filteredCustomers.sublist(
      startIndex,
      endIndex > filteredCustomers.length ? filteredCustomers.length : endIndex,
    );
  }

  // Get total pages
  int get totalPages {
    return (filteredCustomers.length / itemsPerPage.value).ceil();
  }

  // Change page
  void changePage(int page) {
    if (page >= 1 && page <= totalPages) {
      currentPage.value = page;
    }
  }
}
