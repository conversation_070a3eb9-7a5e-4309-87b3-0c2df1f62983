import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/firebase_service.dart';
import '../routes/app_routes.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    final FirebaseService firebaseService = Get.find<FirebaseService>();
    
    // Check if user is signed in
    if (firebaseService.isSignedIn) {
      // If user is signed in and trying to access login page, redirect to dashboard
      if (route == AppRoutes.login) {
        return const RouteSettings(name: AppRoutes.dashboard);
      }
    } else {
      // If user is not signed in and trying to access protected routes, redirect to login
      if (route != AppRoutes.login) {
        return const RouteSettings(name: AppRoutes.login);
      }
    }
    
    return null; // Continue with the original route
  }
}

class AuthGuard extends GetMiddleware {
  @override
  int? get priority => 2;

  @override
  RouteSettings? redirect(String? route) {
    final FirebaseService firebaseService = Get.find<FirebaseService>();
    
    // Protect all routes except login
    if (!firebaseService.isSignedIn && route != AppRoutes.login) {
      return const RouteSettings(name: AppRoutes.login);
    }
    
    return null;
  }
}
