import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/firebase_service.dart';
import '../routes/app_routes.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return GetX<FirebaseService>(
      builder: (firebaseService) {
        // Show loading while checking auth state
        if (firebaseService.user == null && firebaseService.isLoading.value) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // If user is signed in, navigate to dashboard
        if (firebaseService.isSignedIn) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (Get.currentRoute != AppRoutes.dashboard) {
              Get.offAllNamed(AppRoutes.dashboard);
            }
          });
          // Return loading screen while navigation happens
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // If user is not signed in, navigate to login screen
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (Get.currentRoute != AppRoutes.login) {
            Get.offAllNamed(AppRoutes.login);
          }
        });
        // Return loading screen while navigation happens
        return const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
    );
  }
}
