class CustomerModel {
  final String name;
  final String phoneNumber;
  final String location;
  final int orderCount;

  CustomerModel({
    required this.name,
    required this.phoneNumber,
    required this.location,
    required this.orderCount,
  });

  factory CustomerModel.fromJson(Map<String, dynamic> json) {
    return CustomerModel(
      name: json['name'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      location: json['location'] ?? '',
      orderCount: json['orderCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'location': location,
      'orderCount': orderCount,
    };
  }
}
