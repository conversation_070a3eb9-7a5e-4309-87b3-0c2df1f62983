class OrderModel {
  final String orderId;
  final String customerName;
  final String customerPhone;
  final DateTime orderDate;
  final DateTime deliveryDate;
  final int itemCount;
  final double amount;
  final String status;
  final String? priority;
  final String? notes;
  final String? productName; // Added productName field
  final String? productImageUrl; // Added productImageUrl field

  OrderModel({
    required this.orderId,
    required this.customerName,
    required this.customerPhone,
    required this.orderDate,
    required this.deliveryDate,
    required this.itemCount,
    required this.amount,
    required this.status,
    this.priority,
    this.notes,
    this.productName,
    this.productImageUrl,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      orderId: json['orderId'] ?? '',
      customerName: json['customerName'] ?? '',
      customerPhone: json['customerPhone'] ?? '',
      orderDate: DateTime.parse(json['orderDate']),
      deliveryDate: DateTime.parse(json['deliveryDate']),
      itemCount: json['itemCount'] ?? 0,
      amount: (json['amount'] ?? 0).toDouble(),
      status: json['status'] ?? '',
      priority: json['priority'],
      notes: json['notes'],
      productName: json['productName'],
      productImageUrl: json['productImageUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'orderDate': orderDate.toIso8601String(),
      'deliveryDate': deliveryDate.toIso8601String(),
      'itemCount': itemCount,
      'amount': amount,
      'status': status,
      'priority': priority,
      'notes': notes,
      'productName': productName,
      'productImageUrl': productImageUrl,
    };
  }

  OrderModel copyWith({
    String? orderId,
    String? customerName,
    String? customerPhone,
    DateTime? orderDate,
    DateTime? deliveryDate,
    int? itemCount,
    double? amount,
    String? status,
    String? priority,
    String? notes,
    String? productName,
    String? productImageUrl,
  }) {
    return OrderModel(
      orderId: orderId ?? this.orderId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      orderDate: orderDate ?? this.orderDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      itemCount: itemCount ?? this.itemCount,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      notes: notes ?? this.notes,
      productName: productName ?? this.productName,
      productImageUrl: productImageUrl ?? this.productImageUrl,
    );
  }
}
