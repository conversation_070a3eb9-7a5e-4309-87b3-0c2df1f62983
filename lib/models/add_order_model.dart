class AddOrderModel {
  final String customerName;
  final String mobileNumber;
  final String gstNumber;
  final String emailId;
  final String businessName;
  final String businessAddress;
  final String orderId;
  final String productName;
  final double price;
  final int quantity;
  final String payment;
  final String orderStatus;
  final String? priority;
  final String? notes;
  final String? productImagePath;
  final String? productImageUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deliveryDate;

  AddOrderModel({
    required this.customerName,
    required this.mobileNumber,
    required this.gstNumber,
    required this.emailId,
    required this.businessName,
    required this.businessAddress,
    required this.orderId,
    required this.productName,
    required this.price,
    required this.quantity,
    required this.payment,
    required this.orderStatus,
    this.priority,
    this.notes,
    this.productImagePath,
    this.productImageUrl,
    this.createdAt,
    this.updatedAt,
    this.deliveryDate,
  });

  factory AddOrderModel.fromJson(Map<String, dynamic> json) {
    return AddOrderModel(
      customerName: json['customerName'] ?? '',
      mobileNumber: json['mobileNumber'] ?? '',
      gstNumber: json['gstNumber'] ?? '',
      emailId: json['emailId'] ?? '',
      businessName: json['businessName'] ?? '',
      businessAddress: json['businessAddress'] ?? '',
      orderId: json['orderId'] ?? '',
      productName: json['productName'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      quantity: json['quantity'] ?? 0,
      payment: json['payment'] ?? '',
      orderStatus: json['orderStatus'] ?? '',
      priority: json['priority'],
      notes: json['notes'],
      productImagePath: json['productImagePath'],
      productImageUrl: json['productImageUrl'],
      createdAt: json['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : null,
      deliveryDate: json['deliveryDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['deliveryDate'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'customerName': customerName,
      'mobileNumber': mobileNumber,
      'gstNumber': gstNumber,
      'emailId': emailId,
      'businessName': businessName,
      'businessAddress': businessAddress,
      'orderId': orderId,
      'productName': productName,
      'price': price,
      'quantity': quantity,
      'payment': payment,
      'orderStatus': orderStatus,
      'priority': priority,
      'notes': notes,
      'productImagePath': productImagePath,
      'productImageUrl': productImageUrl,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
    };
  }

  AddOrderModel copyWith({
    String? customerName,
    String? mobileNumber,
    String? gstNumber,
    String? emailId,
    String? businessName,
    String? businessAddress,
    String? orderId,
    String? productName,
    double? price,
    int? quantity,
    String? payment,
    String? orderStatus,
    String? priority,
    String? notes,
    String? productImagePath,
    String? productImageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deliveryDate,
  }) {
    return AddOrderModel(
      customerName: customerName ?? this.customerName,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      gstNumber: gstNumber ?? this.gstNumber,
      emailId: emailId ?? this.emailId,
      businessName: businessName ?? this.businessName,
      businessAddress: businessAddress ?? this.businessAddress,
      orderId: orderId ?? this.orderId,
      productName: productName ?? this.productName,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      payment: payment ?? this.payment,
      orderStatus: orderStatus ?? this.orderStatus,
      priority: priority ?? this.priority,
      notes: notes ?? this.notes,
      productImagePath: productImagePath ?? this.productImagePath,
      productImageUrl: productImageUrl ?? this.productImageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deliveryDate: deliveryDate ?? this.deliveryDate,
    );
  }
}
