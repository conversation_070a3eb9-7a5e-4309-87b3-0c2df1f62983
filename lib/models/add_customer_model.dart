class AddCustomerModel {
  final String customerId;
  final String fullName;
  final String mobileNumber;
  final String emailId;
  final String gstNumber;
  final String businessName;
  final String businessAddress;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  AddCustomerModel({
    required this.customerId,
    required this.fullName,
    required this.mobileNumber,
    required this.emailId,
    required this.gstNumber,
    required this.businessName,
    required this.businessAddress,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  factory AddCustomerModel.fromJson(Map<String, dynamic> json) {
    return AddCustomerModel(
      customerId: json['customerId'] ?? '',
      fullName: json['fullName'] ?? '',
      mobileNumber: json['mobileNumber'] ?? '',
      emailId: json['emailId'] ?? '',
      gstNumber: json['gstNumber'] ?? '',
      businessName: json['businessName'] ?? '',
      businessAddress: json['businessAddress'] ?? '',
      notes: json['notes'],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'fullName': fullName,
      'mobileNumber': mobileNumber,
      'emailId': emailId,
      'gstNumber': gstNumber,
      'businessName': businessName,
      'businessAddress': businessAddress,
      'notes': notes,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  AddCustomerModel copyWith({
    String? customerId,
    String? fullName,
    String? mobileNumber,
    String? emailId,
    String? gstNumber,
    String? businessName,
    String? businessAddress,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AddCustomerModel(
      customerId: customerId ?? this.customerId,
      fullName: fullName ?? this.fullName,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      emailId: emailId ?? this.emailId,
      gstNumber: gstNumber ?? this.gstNumber,
      businessName: businessName ?? this.businessName,
      businessAddress: businessAddress ?? this.businessAddress,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
