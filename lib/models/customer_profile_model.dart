class CustomerProfileModel {
  final String name;
  final String phoneNumber;
  final String email;
  final String location;
  final String? businessName;
  final String? gstNumber;
  final String? businessAddress;
  final int totalOrders;
  final double totalSpent;
  final double avgOrderValue;
  final String currentStatus;
  final String? profileImagePath;
  final String? notes;
  final List<ContactLogEntry> contactLog;
  final DateTime? lastOrderDate;

  CustomerProfileModel({
    required this.name,
    required this.phoneNumber,
    required this.email,
    required this.location,
    this.businessName,
    this.gstNumber,
    this.businessAddress,
    required this.totalOrders,
    required this.totalSpent,
    required this.avgOrderValue,
    required this.currentStatus,
    this.profileImagePath,
    this.notes,
    required this.contactLog,
    this.lastOrderDate,
  });

  factory CustomerProfileModel.fromJson(Map<String, dynamic> json) {
    return CustomerProfileModel(
      name: json['name'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      email: json['email'] ?? '',
      location: json['location'] ?? '',
      businessName: json['businessName'],
      gstNumber: json['gstNumber'],
      businessAddress: json['businessAddress'],
      totalOrders: json['totalOrders'] ?? 0,
      totalSpent: (json['totalSpent'] ?? 0).toDouble(),
      avgOrderValue: (json['avgOrderValue'] ?? 0).toDouble(),
      currentStatus: json['currentStatus'] ?? 'Packing',
      profileImagePath: json['profileImagePath'],
      notes: json['notes'],
      contactLog: (json['contactLog'] as List<dynamic>?)
              ?.map((e) => ContactLogEntry.fromJson(e))
              .toList() ??
          [],
      lastOrderDate: json['lastOrderDate'] != null
          ? DateTime.parse(json['lastOrderDate'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
      'location': location,
      'businessName': businessName,
      'gstNumber': gstNumber,
      'businessAddress': businessAddress,
      'totalOrders': totalOrders,
      'totalSpent': totalSpent,
      'avgOrderValue': avgOrderValue,
      'currentStatus': currentStatus,
      'profileImagePath': profileImagePath,
      'notes': notes,
      'contactLog': contactLog.map((e) => e.toJson()).toList(),
      'lastOrderDate': lastOrderDate?.toIso8601String(),
    };
  }

  CustomerProfileModel copyWith({
    String? name,
    String? phoneNumber,
    String? email,
    String? location,
    String? businessName,
    String? gstNumber,
    String? businessAddress,
    int? totalOrders,
    double? totalSpent,
    double? avgOrderValue,
    String? currentStatus,
    String? profileImagePath,
    String? notes,
    List<ContactLogEntry>? contactLog,
    DateTime? lastOrderDate,
  }) {
    return CustomerProfileModel(
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      location: location ?? this.location,
      businessName: businessName ?? this.businessName,
      gstNumber: gstNumber ?? this.gstNumber,
      businessAddress: businessAddress ?? this.businessAddress,
      totalOrders: totalOrders ?? this.totalOrders,
      totalSpent: totalSpent ?? this.totalSpent,
      avgOrderValue: avgOrderValue ?? this.avgOrderValue,
      currentStatus: currentStatus ?? this.currentStatus,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      notes: notes ?? this.notes,
      contactLog: contactLog ?? this.contactLog,
      lastOrderDate: lastOrderDate ?? this.lastOrderDate,
    );
  }
}

class ContactLogEntry {
  final String type; // 'call', 'email', 'message'
  final DateTime timestamp;
  final String? description;

  ContactLogEntry({
    required this.type,
    required this.timestamp,
    this.description,
  });

  factory ContactLogEntry.fromJson(Map<String, dynamic> json) {
    return ContactLogEntry(
      type: json['type'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'timestamp': timestamp.toIso8601String(),
      'description': description,
    };
  }
}

class OrderProgressStep {
  final String title;
  final String date;
  final bool isCompleted;
  final bool isActive;

  OrderProgressStep({
    required this.title,
    required this.date,
    required this.isCompleted,
    required this.isActive,
  });

  factory OrderProgressStep.fromJson(Map<String, dynamic> json) {
    return OrderProgressStep(
      title: json['title'] ?? '',
      date: json['date'] ?? '',
      isCompleted: json['isCompleted'] ?? false,
      isActive: json['isActive'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'date': date,
      'isCompleted': isCompleted,
      'isActive': isActive,
    };
  }
}
