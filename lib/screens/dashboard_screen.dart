import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/smart_svg_icon.dart';
import '../controllers/dashboard_controller.dart';
import '../common/common_sidebar.dart' as cs;

class DashboardScreen extends GetView<DashboardController> {
  const DashboardScreen({super.key});

  List<cs.NavItemData> get navItems => [
    cs.NavItemData(icon: 'home', label: 'Dashboard', index: 0),
    cs.NavItemData(icon: 'person', label: 'Customers List', index: 1),
    cs.NavItemData(icon: 'shopping_cart', label: 'Orders List', index: 2),
  ];

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;
          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: cs.AppMobileBar(),
              drawer: Obx(
                () => cs.AppMobileDrawer(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              body: _buildMainContent(),
            );
          }
          return Row(
            children: [
              Obx(
                () => cs.Sidebar(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: EdgeInsets.all(MySize.size24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          cs.AppHeader(
            title: 'Welcome Back Milan',
            subtitle: "Let's get your orders moving",
            isMobile: false,
            trailing: Row(
              children: [
                SizedBox(width: MySize.size8),
                SmartIcon(
                  assetPath: 'assets/svg/hello_icon.svg',
                  height: 24,
                  width: 24,
                ),
              ],
            ),
          ),
          SizedBox(height: MySize.size32),
          // Dashboard Grid
          // Expanded(child: _buildDashboardGrid()),

          // Content based on search state
          Expanded(
            child: Obx(() {
              if (controller.searchQuery.value.isNotEmpty) {
                return _buildSearchResults();
              } else {
                return _buildDashboardGrid();
              }
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive cross axis count based on available width
        int crossAxisCount = 4; // Default for large screens

        if (constraints.maxWidth < 600) {
          crossAxisCount = 1; // Mobile: 1 column
        } else if (constraints.maxWidth < 900) {
          crossAxisCount = 2; // Tablet: 2 columns
        } else if (constraints.maxWidth < 1200) {
          crossAxisCount = 3; // Small desktop: 3 columns
        } else {
          crossAxisCount = 4; // Large desktop: 4 columns
        }

        return Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          final dashboardItems = controller.getDashboardItems();

          if (dashboardItems.isEmpty) {
            return const Center(
              child: Text('No data available'),
            );
          }

          return GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: MySize.size12,
              mainAxisSpacing: MySize.size12,
              childAspectRatio: 2,
            ),
            itemCount: dashboardItems.length,
            itemBuilder: (context, index) {
              final item = dashboardItems[index];
              return _buildDashboardCard(
                title: item['title'],
                count: item['count'].toString(),
                iconPath: item['iconPath'],
                iconColor: item['iconColor'],
              );
            },
          );
        });
      },
    );
  }

  Widget _buildSearchResults() {
    return Obx(() {
      final searchResults = controller.searchResults;

      if (searchResults.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: MySize.size64,
                color: AppColors.textSecondary,
              ),
              SizedBox(height: MySize.size16),
              Text(
                'No orders found for "${controller.searchQuery.value}"',
                style: TextStyle(
                  fontSize: MySize.size16,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        );
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search results header
          Text(
            'Search Results (${searchResults.length} orders found)',
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: MySize.size16),

          // Search results list
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(MySize.size12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ListView.builder(
                padding: EdgeInsets.all(MySize.size16),
                itemCount: searchResults.length,
                itemBuilder: (context, index) {
                  final order = searchResults[index];
                  return _buildSearchResultCard(order, index);
                },
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildSearchResultCard(dynamic order, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: MySize.size12),
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.circular(MySize.size8),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          // Order info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order.orderId,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: MySize.size4),
                Text(
                  order.customerName,
                  style: TextStyle(
                    fontSize: MySize.size13,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: MySize.size2),
                Text(
                  order.mobileNumber,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Status
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.size12,
              vertical: MySize.size6,
            ),
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(MySize.size16),
            ),
            child: Text(
              order.orderStatus,
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w500,
                color: AppColors.primaryColor,
              ),
            ),
          ),

          SizedBox(width: MySize.size12),

          // Amount
          Text(
            '₹${(order.price * order.quantity).toStringAsFixed(0)}',
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardCard({
    required String title,
    required String count,
    required String iconPath,
    required Color iconColor,
  }) {
    return GestureDetector(
      onTap: () => controller.onCardTap(title, int.tryParse(count) ?? 0),
      child: Container(
        padding: EdgeInsets.all(MySize.size12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(MySize.size10),
          boxShadow: [
            BoxShadow(
              color: const Color(0x0C0C0C0D),
              blurRadius: 4,
              spreadRadius: -4,
              offset: const Offset(0, 4),
            ),
            BoxShadow(
              color: const Color(0x1A0C0C0D),
              blurRadius: 32,
              spreadRadius: -4,
              offset: const Offset(0, 16),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // Icon and Title Row
            Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  width: MySize.size44,
                  height: MySize.size44,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(MySize.size10),
                  ),
                  child: Center(
                    child: SmartIcon(
                      assetPath: iconPath,
                      width: MySize.size24,
                      height: MySize.size24,
                      color: AppColors.blackColor,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: MySize.size20),

            // Count
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  count,
                  style: TextStyle(
                    fontSize: MySize.size40,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(width: MySize.size8),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
