import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../controllers/customers_list_controller.dart';
import '../models/customer_model.dart';
import '../common/common_sidebar.dart' as cs;

class CustomersListScreen extends GetView<CustomersListController> {
  const CustomersListScreen({super.key});

  List<cs.NavItemData> get navItems => [
    cs.NavItemData(icon: 'home', label: 'Dashboard', index: 0),
    cs.NavItemData(icon: 'person', label: 'Customers List', index: 1),
    cs.NavItemData(icon: 'shopping_cart', label: 'Orders List', index: 2),
  ];

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;

          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: cs.AppMobileBar(),
              drawer: Obx(
                () => cs.AppMobileDrawer(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              body: _buildMainContent(),
            );
          }

          return Row(
            children: [
              Obx(
                () => cs.Sidebar(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 768;
        final isMobile = constraints.maxWidth <= 768;

        return Container(
          padding: EdgeInsets.all(isMobile ? MySize.size12 : MySize.size24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top Search Bar
              if (isTablet) _buildTopSearchBar(),

              if (isTablet) SizedBox(height: MySize.size16),

              // Divider
              if (isTablet) Divider(color: AppColors.borderColor, thickness: 1),

              if (isTablet) SizedBox(height: MySize.size24),

              // Header and Action Bar
              cs.AppHeaderAndActionRow(
                title: 'Customers',
                subtitle: 'Manage your customer base and their order history.',
                isMobile: isMobile,
                searchBar:
                    isMobile
                        ? CustomTextField(
                          controller: controller.searchController,
                          hintText: 'Search customers...',
                          onChanged: controller.onSearchChanged,
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(MySize.size12),
                            child: PlatformIcon(
                              iconName: 'search',
                              size: MySize.size18,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          fillColor: Colors.white,
                          borderColor: AppColors.borderColor,
                          borderRadius: MySize.size8,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: MySize.size12,
                            vertical: MySize.size10,
                          ),
                        )
                        : null,
                actionButton:
                    isMobile
                        ? GestureDetector(
                          onTap: controller.onAddCustomerTap,
                          child: Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(
                              vertical: MySize.size12,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primaryColor,
                              borderRadius: BorderRadius.circular(MySize.size8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                PlatformIcon(
                                  iconName: 'add',
                                  size: MySize.size16,
                                  color: AppColors.blackColor,
                                ),
                                SizedBox(width: MySize.size8),
                                Text(
                                  'Add Customer',
                                  style: TextStyle(
                                    fontSize: MySize.size14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.blackColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        : GestureDetector(
                          onTap: controller.onAddCustomerTap,
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: MySize.size16,
                              vertical: MySize.size12,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.primaryColor,
                              borderRadius: BorderRadius.circular(MySize.size8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                PlatformIcon(
                                  iconName: 'add',
                                  size: MySize.size16,
                                  color: AppColors.blackColor,
                                ),
                                SizedBox(width: MySize.size8),
                                Text(
                                  'Add Customer',
                                  style: TextStyle(
                                    fontSize: MySize.size14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.blackColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
              ),

              SizedBox(height: isMobile ? MySize.size16 : MySize.size24),

              // Customers Table
              Expanded(child: _buildCustomersTable(isMobile: isMobile)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTopSearchBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SizedBox(
          width: 300,
          child: CustomTextField(
            controller: TextEditingController(),
            hintText: 'Search Orders',
            prefixIcon: Padding(
              padding: EdgeInsets.all(MySize.size12),
              child: PlatformIcon(
                iconName: 'search',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
            ),
            fillColor: Colors.white,
            borderColor: AppColors.borderColor,
            borderRadius: MySize.size20,
            contentPadding: EdgeInsets.symmetric(
              horizontal: MySize.size16,
              vertical: MySize.size10,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomersTable({bool isMobile = false}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table Header
          if (!isMobile) _buildTableHeader(isMobile: isMobile),

          // Table Content
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              final customers = controller.paginatedCustomers;
              if (customers.isEmpty) {
                return const Center(child: Text('No customers found'));
              }

              if (isMobile) {
                return ListView.builder(
                  padding: EdgeInsets.all(MySize.size8),
                  itemCount: customers.length,
                  itemBuilder: (context, index) {
                    return _buildMobileCustomerCard(customers[index], index);
                  },
                );
              }

              return ListView.builder(
                itemCount: customers.length,
                itemBuilder: (context, index) {
                  return _buildTableRow(
                    customers[index],
                    index,
                    isMobile: isMobile,
                  );
                },
              );
            }),
          ),

          // Pagination
          _buildPagination(),
        ],
      ),
    );
  }

  Widget _buildTableHeader({bool isMobile = false}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.borderColor, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Name
          Expanded(
            flex: 3,
            child: Text(
              'NAME',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          // Phone Number
          Expanded(
            flex: 2,
            child: Text(
              'Phone Number',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          // Location
          Expanded(
            flex: 2,
            child: Text(
              'LOCATION',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          // Orders
          Expanded(
            flex: 1,
            child: Text(
              'ORDERS',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          // Action
          Expanded(
            flex: 1,
            child: Text(
              'ACTION',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(
    CustomerModel customer,
    int index, {
    bool isMobile = false,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Name
          Expanded(
            flex: 3,
            child: Text(
              customer.name,
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Phone Number
          Expanded(
            flex: 2,
            child: Text(
              customer.phoneNumber,
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Location
          Expanded(
            flex: 2,
            child: Text(
              customer.location,
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Orders
          Expanded(
            flex: 1,
            child: Text(
              '${customer.orderCount} Orders',
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Action
          Expanded(
            flex: 1,
            child: Center(
              child: GestureDetector(
                onTap: () => controller.onCustomerActionTap(customer),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.size12,
                    vertical: MySize.size6,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(MySize.size16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      PlatformIcon(
                        iconName: 'visibility',
                        size: MySize.size14,
                        color: AppColors.blackColor,
                      ),
                      SizedBox(width: MySize.size4),
                      Text(
                        'View',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          fontWeight: FontWeight.w500,
                          color: AppColors.blackColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileCustomerCard(CustomerModel customer, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: MySize.size12),
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size8),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Name and Action Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  customer.name,
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => controller.onCustomerActionTap(customer),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.size8,
                    vertical: MySize.size4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(MySize.size12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      PlatformIcon(
                        iconName: 'visibility',
                        size: MySize.size12,
                        color: AppColors.blackColor,
                      ),
                      SizedBox(width: MySize.size4),
                      Text(
                        'View',
                        style: TextStyle(
                          fontSize: MySize.size10,
                          fontWeight: FontWeight.w500,
                          color: AppColors.blackColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size8),

          // Phone Number
          Row(
            children: [
              PlatformIcon(
                iconName: 'phone',
                size: MySize.size14,
                color: AppColors.textSecondary,
              ),
              SizedBox(width: MySize.size6),
              Text(
                customer.phoneNumber,
                style: TextStyle(
                  fontSize: MySize.size13,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size6),

          // Location
          Row(
            children: [
              PlatformIcon(
                iconName: 'location',
                size: MySize.size14,
                color: AppColors.textSecondary,
              ),
              SizedBox(width: MySize.size6),
              Text(
                customer.location,
                style: TextStyle(
                  fontSize: MySize.size13,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size8),

          // Orders Count
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.size8,
              vertical: MySize.size4,
            ),
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(MySize.size12),
            ),
            child: Text(
              '${customer.orderCount} Orders',
              style: TextStyle(
                fontSize: MySize.size11,
                fontWeight: FontWeight.w500,
                color: AppColors.primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPagination() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      child: Obx(() {
        final totalPages = controller.totalPages;
        final currentPage = controller.currentPage.value;

        return Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Previous Button
            GestureDetector(
              onTap:
                  currentPage > 1
                      ? () => controller.changePage(currentPage - 1)
                      : null,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size12,
                  vertical: MySize.size8,
                ),

                child: PlatformIcon(
                  iconName: 'chevron_left',
                  size: MySize.size16,
                  color:
                      currentPage > 1
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                ),
              ),
            ),

            SizedBox(width: MySize.size4),

            // Page Numbers
            ...List.generate(totalPages > 5 ? 5 : totalPages, (index) {
              int pageNumber = index + 1;
              if (totalPages > 5 && currentPage > 3) {
                pageNumber = currentPage - 2 + index;
                if (pageNumber > totalPages)
                  pageNumber = totalPages - 4 + index;
              }

              return GestureDetector(
                onTap: () => controller.changePage(pageNumber),
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: MySize.size2),
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.size12,
                    vertical: MySize.size8,
                  ),
                  decoration: BoxDecoration(
                    color:
                        pageNumber == currentPage
                            ? AppColors.primaryColor
                            : Colors.white,
                    border: Border.all(color: AppColors.primaryColor),
                    borderRadius: BorderRadius.circular(MySize.size6),
                  ),
                  child: Text(
                    pageNumber.toString(),
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                      color:
                          pageNumber == currentPage
                              ? AppColors.blackColor
                              : AppColors.textPrimary,
                    ),
                  ),
                ),
              );
            }),

            SizedBox(width: MySize.size4),

            // Next Button
            GestureDetector(
              onTap:
                  currentPage < totalPages
                      ? () => controller.changePage(currentPage + 1)
                      : null,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size12,
                  vertical: MySize.size8,
                ),

                child: PlatformIcon(
                  iconName: 'chevron_right',
                  size: MySize.size16,
                  color:
                      currentPage < totalPages
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}
