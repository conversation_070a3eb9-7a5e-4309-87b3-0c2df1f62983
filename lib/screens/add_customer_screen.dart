import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/smart_svg_icon.dart';
import '../widgets/country_picker.dart';
import '../controllers/add_customer_controller.dart';
import '../common/common_sidebar.dart' as cs;

class AddCustomerScreen extends GetView<AddCustomerController> {
  const AddCustomerScreen({super.key});

  List<cs.NavItemData> get navItems => [
    cs.NavItemData(icon: 'home', label: 'Dashboard', index: 0),
    cs.NavItemData(icon: 'person', label: 'Customers List', index: 1),
    cs.NavItemData(icon: 'shopping_cart', label: 'Orders List', index: 2),
  ];

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;
          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: cs.AppMobileBar(),
              drawer: Obx(
                () => cs.AppMobileDrawer(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              body: _buildMainContent(),
            );
          }
          return Row(
            children: [
              Obx(
                () => cs.Sidebar(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = constraints.maxWidth <= 768;
        return Container(
          padding: EdgeInsets.all(isMobile ? MySize.size12 : MySize.size24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              cs.AppHeader(
                title: 'Add New Customer',
                subtitle:
                    'Add customer details to link with orders and business information',
                isMobile: isMobile,
              ),
              SizedBox(height: MySize.size24),
              // Form Content
              Expanded(
                child: SingleChildScrollView(
                  child: _buildFormContent(isMobile: isMobile),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFormContent({bool isMobile = false}) {
    return Column(
      children: [
        // Contact Person Details Section
        _buildContactPersonDetailsCard(),

        SizedBox(height: MySize.size24),

        // Business Details Section
        _buildBusinessDetailsCard(),

        SizedBox(height: MySize.size32),

        // Add Customer Button
        _buildAddCustomerButton(),
      ],
    );
  }

  Widget _buildContactPersonDetailsCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              PlatformIcon(
                iconName: 'person',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Contact Person Details',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size24),

          // Form Fields Row
          LayoutBuilder(
            builder: (context, constraints) {
              final isMobile = constraints.maxWidth <= 600;

              if (isMobile) {
                return Column(
                  children: [
                    _buildFormField(
                      label: 'Full Name',
                      controller: controller.fullNameController,
                      hintText: 'Enter Full Name',
                      isRequired: true,
                    ),
                    SizedBox(height: MySize.size16),
                    _buildMobileNumberField(),
                    SizedBox(height: MySize.size16),
                    _buildFormField(
                      label: 'Email ID',
                      controller: controller.emailIdController,
                      hintText: '<EMAIL>',
                      isRequired: true,
                      keyboardType: TextInputType.emailAddress,
                    ),
                  ],
                );
              }

              return Column(
                children: [
                  // First Row: Full Name and Mobile Number
                  Row(
                    children: [
                      Expanded(
                        child: _buildFormField(
                          label: 'Full Name',
                          controller: controller.fullNameController,
                          hintText: 'Enter Full Name',
                          isRequired: true,
                        ),
                      ),
                      SizedBox(width: MySize.size24),
                      Expanded(child: _buildMobileNumberField()),
                    ],
                  ),

                  SizedBox(height: MySize.size20),

                  // Second Row: Email ID
                  Row(
                    children: [
                      Expanded(
                        child: _buildFormField(
                          label: 'Email ID',
                          controller: controller.emailIdController,
                          hintText: '<EMAIL>',
                          isRequired: true,
                          keyboardType: TextInputType.emailAddress,
                        ),
                      ),
                      Expanded(
                        child: Container(),
                      ), // Empty space to maintain layout
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessDetailsCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              PlatformIcon(
                iconName: 'building',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Business Details',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size24),

          // Form Fields
          LayoutBuilder(
            builder: (context, constraints) {
              final isMobile = constraints.maxWidth <= 600;

              if (isMobile) {
                return Column(
                  children: [
                    _buildFormField(
                      label: 'GST Number',
                      controller: controller.gstNumberController,
                      hintText: '22AAAAA0000A1Z5',
                    ),
                    SizedBox(height: MySize.size16),
                    _buildFormField(
                      label: 'Business Name',
                      controller: controller.businessNameController,
                      hintText: 'Enter Business Name',
                    ),
                    SizedBox(height: MySize.size16),
                    _buildFormField(
                      label: 'Business Address',
                      controller: controller.businessAddressController,
                      hintText: 'Enter Business Address',
                      maxLines: 3,
                    ),
                  ],
                );
              }

              return Column(
                children: [
                  // First Row: GST Number and Business Name
                  Row(
                    children: [
                      Expanded(
                        child: _buildFormField(
                          label: 'GST Number',
                          controller: controller.gstNumberController,
                          hintText: '22AAAAA0000A1Z5',
                        ),
                      ),
                      SizedBox(width: MySize.size24),
                      Expanded(
                        child: _buildFormField(
                          label: 'Business Name',
                          controller: controller.businessNameController,
                          hintText: 'Enter Business Name',
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: MySize.size20),

                  // Second Row: Business Address
                  Row(
                    children: [
                      Expanded(
                        child: _buildFormField(
                          label: 'Business Address',
                          controller: controller.businessAddressController,
                          hintText: 'Enter Business Address',
                          maxLines: 3,
                        ),
                      ),
                      Expanded(
                        child: Container(),
                      ), // Empty space to maintain layout
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    bool isRequired = false,
    int maxLines = 1,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
            children: [
              if (isRequired)
                TextSpan(
                  text: ' *',
                  style: TextStyle(color: Colors.red, fontSize: MySize.size14),
                ),
            ],
          ),
        ),
        SizedBox(height: MySize.size8),
        CustomTextField(
          controller: controller,
          hintText: hintText,
          maxLines: maxLines,
          keyboardType: keyboardType,
          fillColor: Colors.white,
          borderColor: AppColors.borderColor,
          borderRadius: MySize.size8,
          contentPadding: EdgeInsets.symmetric(
            horizontal: MySize.size16,
            vertical: MySize.size12,
          ),
        ),
      ],
    );
  }

  Widget _buildMobileNumberField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: 'Mobile Number',
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
            children: [
              TextSpan(
                text: ' *',
                style: TextStyle(color: Colors.red, fontSize: MySize.size14),
              ),
            ],
          ),
        ),
        SizedBox(height: MySize.size8),
        Row(
          children: [
            // Country Picker
            Obx(
              () => CountryPicker(
                selectedCountry: controller.selectedCountry.value,
                onCountrySelected: controller.selectCountry,
                width: MySize.size62,
              ),
            ),
            SizedBox(width: MySize.size8),
            // Mobile Number Field
            Expanded(
              child: CustomTextField(
                controller: controller.mobileNumberController,
                hintText: 'Enter 10-Digit Mobile Number',
                keyboardType: TextInputType.phone,
                fillColor: Colors.white,
                borderColor: AppColors.borderColor,
                borderRadius: MySize.size8,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: MySize.size16,
                  vertical: MySize.size12,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAddCustomerButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: SizedBox(
        width: 200,
        child: Obx(
          () => ElevatedButton(
            onPressed:
                controller.isLoading.value ? null : controller.onAddCustomerTap,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: AppColors.blackColor,
              padding: EdgeInsets.symmetric(vertical: MySize.size18),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(MySize.size40),
              ),
              elevation: 0,
            ),
            child:
                controller.isLoading.value
                    ? SizedBox(
                      height: MySize.size20,
                      width: MySize.size20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.blackColor,
                        ),
                      ),
                    )
                    : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SmartIcon(
                          assetPath: 'assets/icons/tick_icon.svg',
                          height: 18,
                          width: 18,
                          color: AppColors.blackColor,
                        ),
                        SizedBox(width: MySize.size8),
                        Text(
                          'Add Customer',
                          style: TextStyle(
                            fontSize: MySize.size16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
          ),
        ),
      ),
    );
  }
}
