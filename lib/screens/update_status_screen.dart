import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../controllers/update_status_controller.dart';
import '../common/common_sidebar.dart' as cs;

class UpdateStatusScreen extends GetView<UpdateStatusController> {
  const UpdateStatusScreen({super.key});

  List<cs.NavItemData> get navItems => [
    cs.NavItemData(icon: 'home', label: 'Dashboard', index: 0),
    cs.NavItemData(icon: 'person', label: 'Customers List', index: 1),
    cs.NavItemData(icon: 'shopping_cart', label: 'Orders List', index: 2),
  ];

  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;
          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: cs.AppMobileBar(),
              drawer: Obx(
                () => cs.AppMobileDrawer(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              body: _buildMainContent(),
            );
          }
          return Row(
            children: [
              Obx(
                () => cs.Sidebar(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = constraints.maxWidth <= 768;
        return Padding(
          padding: EdgeInsets.all(isMobile ? MySize.size16 : MySize.size24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: cs.AppHeader(
                      title: 'Order Status: ${controller.selectedOrderStatus.value}',
                      subtitle:
                          'Your order has moved to the packaging stage. Items are now being packed in protective pouches before final processing.',
                      isMobile: isMobile,
                    ),
                  ),
                  // Refresh button
                  Obx(() => IconButton(
                    onPressed: controller.isRefreshing.value
                        ? null
                        : controller.refreshOrderManually,
                    icon: controller.isRefreshing.value
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primaryColor,
                              ),
                            ),
                          )
                        : PlatformIcon(
                            iconName: 'refresh',
                            size: MySize.size24,
                            color: AppColors.primaryColor,
                          ),
                    tooltip: 'Refresh order data',
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.primaryColor.withValues(alpha: 0.1),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size8),
                      ),
                      padding: EdgeInsets.all(MySize.size12),
                    ),
                  )),
                ],
              ),
              SizedBox(height: MySize.size24),
              // Main content area
              Expanded(
                child:
                    isMobile
                        ? _buildMobileLayout()
                        : Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Left side - Order form
                            Expanded(flex: 2, child: _buildOrderForm()),
                            SizedBox(width: MySize.size24),
                            // Right side - Status progress and order details
                            Expanded(flex: 1, child: _buildRightSide()),
                          ],
                        ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Order form
          _buildOrderForm(),

          SizedBox(height: MySize.size24),

          // Status progress and order details
          _buildRightSide(),
        ],
      ),
    );
  }

  Widget _buildOrderForm() {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Status Dropdown
          Text(
            'Order Status',
            style: TextStyle(
              fontSize: MySize.size16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: MySize.size12),
          Obx(
            () => Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: MySize.size16),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.borderColor),
                borderRadius: BorderRadius.circular(MySize.size8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: controller.selectedOrderStatus.value,
                  isExpanded: true,
                  items:
                      controller.orderStatusOptions.map((String status) {
                        return DropdownMenuItem<String>(
                          value: status,
                          child: Text(
                            status,
                            style: TextStyle(
                              fontSize: MySize.size14,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        );
                      }).toList(),
                  onChanged: controller.onOrderStatusChanged,
                  icon: PlatformIcon(
                    iconName: 'chevron_down',
                    size: MySize.size20,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),
          ),

          SizedBox(height: MySize.size24),

          // Product Photo
          Text(
            'Product Photo',
            style: TextStyle(
              fontSize: MySize.size16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: MySize.size12),
          Obx(() {
            final order = controller.currentOrder.value;
            return Container(
              width: double.infinity,
              height: MySize.size200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(MySize.size8),
                border: Border.all(color: AppColors.borderColor),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(MySize.size8),
                child: _buildProductImage(order),
              ),
            );
          }),

          SizedBox(height: MySize.size24),

          // Remarks
          Text(
            'Remarks',
            style: TextStyle(
              fontSize: MySize.size16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: MySize.size12),
          CustomTextField(
            controller: controller.remarksController,
            hintText: 'Enter remarks...',
            maxLines: 4,
            fillColor: AppColors.backgroundColor,
            borderColor: AppColors.borderColor,
            borderRadius: MySize.size8,
            contentPadding: EdgeInsets.all(MySize.size16),
          ),

          SizedBox(height: MySize.size32),

          // Update Order Status Button
          SizedBox(
            width: double.infinity,
            child: Obx(
              () => ElevatedButton(
                onPressed:
                    controller.isLoading.value
                        ? null
                        : controller.onUpdateOrderStatus,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: MySize.size16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(MySize.size8),
                  ),
                ),
                child:
                    controller.isLoading.value
                        ? SizedBox(
                          height: MySize.size20,
                          width: MySize.size20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            PlatformIcon(
                              iconName: 'check',
                              size: MySize.size18,
                              color: Colors.white,
                            ),
                            SizedBox(width: MySize.size8),
                            Text(
                              'Update Order Status',
                              style: TextStyle(
                                fontSize: MySize.size16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRightSide() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Status Progress
          _buildStatusProgress(),

          SizedBox(height: MySize.size24),

          // Order Details
          _buildOrderDetails(),
        ],
      ),
    );
  }

  Widget _buildStatusProgress() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Status Progress',
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              // Refresh indicator for status progress
              Obx(() => controller.isRefreshing.value
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.primaryColor,
                        ),
                      ),
                    )
                  : SizedBox.shrink()),
            ],
          ),
          SizedBox(height: MySize.size16),

          Obx(() => Column(
            children: controller.statusProgress.map(
              (status) => _buildStatusItem(
                title: status['title'],
                date: status['date'],
                time: status['time'],
                isCompleted: status['isCompleted'],
                icon: status['icon'],
              ),
            ).toList(),
          )),
        ],
      ),
    );
  }

  Widget _buildStatusItem({
    required String title,
    required String date,
    required String time,
    required bool isCompleted,
    required String icon,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: MySize.size16),
      child: Row(
        children: [
          // Icon
          Container(
            width: MySize.size40,
            height: MySize.size40,
            decoration: BoxDecoration(
              color:
                  isCompleted
                      ? AppColors.primaryColor
                      : AppColors.backgroundColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: PlatformIcon(
                iconName: isCompleted ? 'check' : icon,
                size: MySize.size20,
                color: isCompleted ? Colors.white : AppColors.textSecondary,
              ),
            ),
          ),

          SizedBox(width: MySize.size12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  '$date, $time',
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Status indicator
          if (isCompleted)
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: MySize.size8,
                vertical: MySize.size4,
              ),
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(MySize.size4),
              ),
              child: Text(
                'Completed',
                style: TextStyle(
                  fontSize: MySize.size10,
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            )
          else
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: MySize.size8,
                vertical: MySize.size4,
              ),
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(MySize.size4),
              ),
              child: Text(
                'Pending',
                style: TextStyle(
                  fontSize: MySize.size10,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildOrderDetails() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Details',
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: MySize.size16),

          Obx(() {
            final order = controller.currentOrder.value;
            if (order == null) {
              return Text(
                'No order data available',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textSecondary,
                ),
              );
            }

            return Column(
              children: [
                _buildDetailRow('Order ID:', order.orderId),
                _buildDetailRow('Customer:', order.customerName),
                _buildDetailRow(
                  'Order Date:',
                  controller.formatDate(order.orderDate),
                ),
                _buildDetailRow('Total:', '₹ ${order.amount.toInt()}'),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: MySize.size12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: MySize.size14,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  // Build product image widget based on order data
  Widget _buildProductImage(dynamic order) {
    // Check if order has a product image URL from Firebase
    if (order != null && order.productImageUrl != null && order.productImageUrl.isNotEmpty) {
      // Display the actual product image from Firebase
      return Image.network(
        order.productImageUrl,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            color: AppColors.backgroundColor,
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          // If image fails to load, show placeholder
          return _buildImagePlaceholder();
        },
      );
    }

    // If no image URL is available, show placeholder
    return _buildImagePlaceholder();
  }

  // Build image placeholder when no image is available
  Widget _buildImagePlaceholder() {
    return Container(
      color: AppColors.backgroundColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            PlatformIcon(
              iconName: 'image',
              size: MySize.size48,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: MySize.size8),
            Text(
              'No Product Image',
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
