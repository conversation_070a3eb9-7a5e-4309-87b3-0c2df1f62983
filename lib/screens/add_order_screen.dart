import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:io';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';

import '../models/add_customer_model.dart';
import '../controllers/add_order_controller.dart';
import '../common/common_sidebar.dart' as cs;

class AddOrderScreen extends GetView<AddOrderController> {
  const AddOrderScreen({super.key});

  List<cs.NavItemData> get navItems => [
    cs.NavItemData(icon: 'home', label: 'Dashboard', index: 0),
    cs.NavItemData(icon: 'person', label: 'Customers List', index: 1),
    cs.NavItemData(icon: 'shopping_cart', label: 'Orders List', index: 2),
  ];

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;

          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: cs.AppMobileBar(),
              drawer: Obx(
                () => cs.AppMobileDrawer(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              body: _buildMainContent(),
            );
          }

          return Row(
            children: [
              Obx(
                () => cs.Sidebar(
                  selectedIndex: controller.selectedNavIndex.value,
                  items: navItems,
                  onNavTap: controller.selectNavItem,
                ),
              ),
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = constraints.maxWidth <= 768;
        return Container(
          padding: EdgeInsets.all(isMobile ? MySize.size12 : MySize.size24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              cs.AppHeader(
                title: 'Add New Order',
                subtitle:
                    "Track every document you've placed sorted by date and status.",
                isMobile: isMobile,
              ),
              SizedBox(height: MySize.size24),
              // Form Content
              Expanded(
                child: SingleChildScrollView(
                  child: _buildFormContent(isMobile: isMobile),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFormContent({bool isMobile = false}) {
    if (isMobile) {
      return Column(
        children: [
          _buildCustomerDetailsCard(),
          SizedBox(height: MySize.size16),
          _buildBusinessInformationCard(),
          SizedBox(height: MySize.size16),
          _buildOrderDetailsCard(),
          SizedBox(height: MySize.size24),
          _buildAddOrderButton(),
        ],
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Column - Customer Details & Business Information
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildCustomerDetailsCard(),
              SizedBox(height: MySize.size24),
              _buildBusinessInformationCard(),
            ],
          ),
        ),

        SizedBox(width: MySize.size24),

        // Right Column - Order Details
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildOrderDetailsCard(),
              SizedBox(height: MySize.size24),
              _buildAddOrderButton(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerDetailsCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              PlatformIcon(
                iconName: 'person',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Customer Details',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size20),

          // Customer Name
          _buildFormField(
            label: 'Customer Name',
            controller: controller.customerNameController,
            hintText: 'Enter customer name',
          ),

          SizedBox(height: MySize.size16),

          // Mobile Number
          _buildMobileNumberField(),

          SizedBox(height: MySize.size16),

          // GST Number
          _buildFormField(
            label: 'GST Number',
            controller: controller.gstNumberController,
            hintText: 'Enter GST number',
          ),

          SizedBox(height: MySize.size16),

          // Email ID
          _buildFormField(
            label: 'Email ID',
            controller: controller.emailIdController,
            hintText: 'Enter email address',
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessInformationCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              PlatformIcon(
                iconName: 'building',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Business Information',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size20),

          // Business Name
          _buildFormField(
            label: 'Business Name',
            controller: controller.businessNameController,
            hintText: 'Enter Business Name',
          ),

          SizedBox(height: MySize.size16),

          // Business Address
          _buildFormField(
            label: 'Business Address',
            controller: controller.businessAddressController,
            hintText: 'Enter Business Address',
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetailsCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              PlatformIcon(
                iconName: 'order',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
              SizedBox(width: MySize.size8),
              Text(
                'Order Details',
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size20),

          // Product Image Upload
          _buildImageUploadSection(),

          SizedBox(height: MySize.size20),

          // Order ID
          _buildFormField(
            label: 'Order ID',
            controller: controller.orderIdController,
            hintText: 'Auto-generated',
          ),

          SizedBox(height: MySize.size16),

          // Product Name
          _buildFormField(
            label: 'Product Name',
            controller: controller.productNameController,
            hintText: 'Enter product name',
          ),

          SizedBox(height: MySize.size16),

          // Price and Quantity Row
          Row(
            children: [
              Expanded(
                child: _buildFormField(
                  label: 'Price',
                  controller: controller.priceController,
                  hintText: 'Enter price',
                  prefixText: '₹ ',
                ),
              ),
              SizedBox(width: MySize.size16),
              Expanded(
                child: _buildFormField(
                  label: 'Quantity',
                  controller: controller.quantityController,
                  hintText: 'Enter quantity',
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size16),

          // Payment and Order Status Row
          Row(
            children: [
              Expanded(
                child: _buildDropdownField(
                  label: 'Payment',
                  value: controller.selectedPayment,
                  items: controller.paymentOptions,
                  onChanged: controller.selectPayment,
                ),
              ),
              SizedBox(width: MySize.size16),
              Expanded(
                child: _buildDropdownField(
                  label: 'Order Status',
                  value: controller.selectedOrderStatus,
                  items: controller.orderStatusOptions,
                  onChanged: controller.selectOrderStatus,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    Widget? prefixWidget,
    String? prefixText,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        CustomTextField(
          controller: controller,
          hintText: hintText,
          maxLines: maxLines,
          prefixIcon:
              prefixWidget ??
              (prefixText != null
                  ? Padding(
                    padding: EdgeInsets.symmetric(horizontal: MySize.size12),
                    child: Center(
                      widthFactor: 1.0,
                      child: Text(
                        prefixText,
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  )
                  : null),
          fillColor: Colors.white,
          borderColor: AppColors.borderColor,
          borderRadius: MySize.size8,
          contentPadding: EdgeInsets.symmetric(
            horizontal: MySize.size16,
            vertical: MySize.size12,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField({
    required String label,
    required RxString value,
    required List<String> items,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        Obx(
          () => Container(
            padding: EdgeInsets.symmetric(horizontal: MySize.size16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: AppColors.borderColor),
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: value.value,
                isExpanded: true,
                icon: PlatformIcon(
                  iconName: 'chevron_down',
                  size: MySize.size20,
                  color: AppColors.textSecondary,
                ),
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textPrimary,
                ),
                items:
                    items.map((String item) {
                      return DropdownMenuItem<String>(
                        value: item,
                        child: Text(item),
                      );
                    }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    onChanged(newValue);
                  }
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImageUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Upload Area
        GestureDetector(
          onTap: controller.onUploadImageTap,
          child: Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              borderRadius: BorderRadius.circular(MySize.size8),
              border: Border.all(
                color: AppColors.borderColor,
                style: BorderStyle.solid,
              ),
            ),
            child: Obx(
              () =>
                  _hasImageSelected()
                      ? Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(MySize.size8),
                            child: _buildImageWidget(),
                          ),
                          // Remove button
                          Positioned(
                            top: 8,
                            right: 8,
                            child: GestureDetector(
                              onTap: () {
                                controller.productImagePath.value = '';
                                controller.productImageUrl.value = '';
                                controller.productImageBytes.value = null;
                              },
                              child: Container(
                                padding: EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                      : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Default product image placeholder
                          Container(
                            width: 80,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(MySize.size4),
                            ),
                            child: Stack(
                              children: [
                                // Simulate stacked papers
                                Positioned(
                                  left: 5,
                                  top: 5,
                                  child: Container(
                                    width: 70,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(
                                        MySize.size2,
                                      ),
                                      border: Border.all(
                                        color: Colors.grey[400]!,
                                      ),
                                    ),
                                  ),
                                ),
                                Positioned(
                                  left: 10,
                                  top: 10,
                                  child: Container(
                                    width: 70,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(
                                        MySize.size2,
                                      ),
                                      border: Border.all(
                                        color: Colors.grey[400]!,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: MySize.size12),
                          PlatformIcon(
                            iconName: 'camera',
                            size: MySize.size24,
                            color: AppColors.primaryColor,
                          ),
                          SizedBox(height: MySize.size8),
                          Text(
                            'Upload A Product Image',
                            style: TextStyle(
                              fontSize: MySize.size14,
                              fontWeight: FontWeight.w500,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          SizedBox(height: MySize.size4),
                          Text(
                            'Tap to select from gallery or camera',
                            style: TextStyle(
                              fontSize: MySize.size12,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
            ),
          ),
        ),

        SizedBox(height: MySize.size8),

        // Image format info
        Text(
          'The image format is .jpg, .jpeg, .png and a minimum size of 300 x 300px. (For optimal image use a minimum size of 700 x 700 px).',
          style: TextStyle(
            fontSize: MySize.size12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildAddOrderButton() {
    return SizedBox(
      width: double.infinity,
      child: Obx(
        () => ElevatedButton(
          onPressed:
              controller.isLoading.value ? null : controller.onAddOrderTap,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryColor,
            foregroundColor: AppColors.blackColor,
            padding: EdgeInsets.symmetric(vertical: MySize.size16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            elevation: 0,
          ),
          child:
              controller.isLoading.value
                  ? SizedBox(
                    height: MySize.size20,
                    width: MySize.size20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.blackColor,
                      ),
                    ),
                  )
                  : Text(
                    'Add Order',
                    style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
        ),
      ),
    );
  }

  Widget _buildMobileNumberField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Phone Number',
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: MySize.size8),
        Obx(() {
          if (controller.isLoadingCustomers.value) {
            return Container(
              height: MySize.size48,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(MySize.size8),
                border: Border.all(color: AppColors.borderColor),
              ),
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            );
          }

          return Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(MySize.size8),
              border: Border.all(color: AppColors.borderColor),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<AddCustomerModel?>(
                value: controller.selectedCustomer.value,
                hint: Padding(
                  padding: EdgeInsets.symmetric(horizontal: MySize.size16),
                  child: Text(
                    'Select a customer by phone number',
                    style: TextStyle(
                      fontSize: MySize.size14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                isExpanded: true,
                icon: Padding(
                  padding: EdgeInsets.only(right: MySize.size16),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColors.textSecondary,
                  ),
                ),
                // Custom builder for selected item - shows only phone number
                selectedItemBuilder: (BuildContext context) {
                  return [
                    // For null value (clear selection)
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: MySize.size16),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          'Select a customer',
                          style: TextStyle(
                            fontSize: MySize.size14,
                            color: AppColors.textSecondary,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ),
                    // For customer selections - show only phone number
                    ...controller.customers.map((customer) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: MySize.size16),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            customer.mobileNumber,
                            style: TextStyle(
                              fontSize: MySize.size14,
                              fontWeight: FontWeight.w500,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ),
                      );
                    }),
                  ];
                },
                items: [
                  // Option to clear selection
                  DropdownMenuItem<AddCustomerModel?>(
                    value: null,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: MySize.size16),
                      child: Text(
                        'Select a customer',
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: AppColors.textSecondary,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ),
                  // Customer options
                  ...controller.customers.map((customer) {
                    return DropdownMenuItem<AddCustomerModel?>(
                      value: customer,
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: MySize.size16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              customer.mobileNumber,
                              style: TextStyle(
                                fontSize: MySize.size14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            Text(
                              customer.fullName,
                              style: TextStyle(
                                fontSize: MySize.size12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                ],
                onChanged: controller.selectCustomer,
              ),
            ),
          );
        }),
      ],
    );
  }

  // Check if an image has been selected
  bool _hasImageSelected() {
    return controller.productImageUrl.value.isNotEmpty ||
        controller.productImageBytes.value != null ||
        (!kIsWeb && controller.productImagePath.value.isNotEmpty);
  }

  // Build image widget that works for both web and mobile
  Widget _buildImageWidget() {
    // If we have a Firebase URL, always use that (uploaded image)
    if (controller.productImageUrl.value.isNotEmpty) {
      return Image.network(
        controller.productImageUrl.value,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, color: Colors.red),
                Text('Failed to load uploaded image'),
              ],
            ),
          );
        },
      );
    }

    // For local preview before upload
    if (kIsWeb && controller.productImageBytes.value != null) {
      // On web, use Image.memory with the bytes - no conditions, just display
      return Image.memory(
        controller.productImageBytes.value!,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, color: Colors.red),
                Text('Failed to load image preview'),
              ],
            ),
          );
        },
      );
    } else if (!kIsWeb && controller.productImagePath.value.isNotEmpty) {
      // On mobile, use File - no conditions, just display
      return Image.file(
        File(controller.productImagePath.value),
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, color: Colors.red),
                Text('Failed to load image preview'),
              ],
            ),
          );
        },
      );
    }

    // Fallback - no image selected
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.image_not_supported, color: Colors.grey),
          Text('No image selected'),
        ],
      ),
    );
  }
}
